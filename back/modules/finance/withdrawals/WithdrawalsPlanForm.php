<?php

declare(strict_types=1);

namespace app\back\modules\finance\withdrawals;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Str;
use app\back\components\RichTable;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\modules\finance\components\Permutator;
use app\back\modules\finance\components\WithdrawalsRestrictionManager;
use app\back\repositories\Brands;
use app\back\repositories\Employees;
use app\back\repositories\Sites;
use app\back\repositories\UserTransactions;
use app\back\repositories\WithdrawalPredictions;
use app\back\repositories\Withdrawals;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class WithdrawalsPlanForm
{
    use WithdrawalsMoneyHelper;
    use RichTable {
        responseValues as formGridResponseValues;
    }
    use AllowedSitesForWithdrawalsValidate;

    public const string DATETIME_FORMAT = 'Y-m-d H:i';

    #[IdValidator]
    #[CallableValidator([self::class, 'allowedSitesForWithdrawalsValidate'])]
    public int $siteId;

    #[IdValidator]
    #[CallableValidator([self::class, 'withdrawalTestUserValidate'])]
    public int $userId;

    #[IntValidator(1)]
    #[CallableValidator([self::class, 'sumOrCountLimitValidator'])]
    public ?int $limitCount = 1;

    #[MoneyValidator(min: 1)]
    #[CallableValidator([self::class, 'sumOrCountLimitValidator'])]
    public ?string $limitSum = null;

    #[MatchValidator('#P\d+D#')]
    public string $period = 'P1D';

    #[DateTimeValidator]
    public ?string $plannedAt = null;

    #[StringValidator(max: 10000)]
    public ?string $transactionIds = null;

    private array $combinationsList;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        public readonly WithdrawalsRestrictionManager $restrictionManager,
        private readonly ConnectionInterface $db,
        private readonly Sites $sites,
        private readonly Permutator $pm,
        private readonly BaseAuthAccess $bac,
        private readonly Brands $brands,
    ) {
    }

    protected function blocks(): array
    {
        $this->fillFormValues();
        return [
            [
                $this->dateCell(4, 'plannedAt', 'Withdrawals start at', [
                    'enableTime' => true,
                    'dateFormat' => self::DATETIME_FORMAT,
                ]),
                $this->selectCell(4, 'transactionIds', 'Total sum', [
                    'list' => $this->combinationsList,
                    'multiple' => false,
                ]),
                $this->submitCell(4, 'Calculate', [
                    'buttonStyle' => 'btn-secondary'
                ]),
            ],
            [
                $this->textInputCell(3, 'limitCount', 'Count'),
                $this->textStaticCell(1, 'or', ''),
                $this->textInputCell(3, 'limitSum', 'Sum'),
                $this->textStaticCell(2, 'per', ''),
                $this->selectCell(3, 'period', 'Period', [
                    'multiple' => false,
                    'size' => 'sm',
                    'list' => array_map(static fn($n) => [
                        'id' => "P{$n}D",
                        'name' => "$n Day",
                    ], range(1, 7)),
                ]),
            ],
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Transaction id', 'code' => 'transactionId'],
            ['name' => 'Sum', 'code' => 'sum'],
            ['name' => 'Planned at', 'code' => 'plannedAt'],
            ['name' => ' ', 'slotName' => 'remove']
        ];
    }

    public function data(): array
    {
        $curPlannedAt = new \DateTime($this->plannedAt);
        $periodSeconds = (new \DateTime(date(DateHelper::DATETIME_FORMAT_PHP, 0)))
            ->add(new \DateInterval($this->period))
            ->getTimestamp();
        $calcPeriodDelayRate = match (true) {
            !empty($this->limitCount) => fn($sum) => 1 / $this->limitCount,
            !empty($this->limitSum) => fn($sum) => $sum / $this->limitSum,
        };

        $responseRows = [];
        foreach ($this->limitedAndSortedWithdrawalToPlan() as $i => $row) {
            $periodDelayRate = $calcPeriodDelayRate($row['sum']);
            if ($i !== 0 && $periodDelayRate > 1) {
                $preDelaySeconds = ceil(($periodDelayRate - 1) * $periodSeconds);
                $curPlannedAt->add(new \DateInterval("PT{$preDelaySeconds}S"));
                $periodDelayRate = 1;
            }
            $responseRows[] = [
                'id' => null,
                'aborted_by' => false,
                'transactionId' => $row['transaction_id'],
                'createdAt' => $row['created_at'],
                'plannedAt' => $curPlannedAt->format(self::DATETIME_FORMAT),
                'sum' => $this->formatMoney($row['sum'], $row['currency']),
            ];
            $curPlannedAt = $curPlannedAt->add(new \DateInterval('PT' . ceil($periodDelayRate * $periodSeconds) . 'S'));
        }

        $this->mergeWithAlreadyPlannedWithdrawals($responseRows);

        return $responseRows;
    }

    private function limitedAndSortedWithdrawalToPlan(): array
    {
        $rows = $this->getWithdrawalsToPlan();

        if (isset($this->limitSum)) {
            usort($rows, fn($rowA, $rowB) => abs($rowA['sum'] - $this->limitSum) <=> abs($rowB['sum'] - $this->limitSum));
        }

        if (empty($this->transactionIds)) {
            return $rows;
        }

        $transactionIds = array_flip(explode(',', $this->transactionIds));
        return array_filter($rows, static fn($row) => array_key_exists($row['transaction_id'], $transactionIds));
    }

    private function mergeWithAlreadyPlannedWithdrawals(array &$rows): void
    {
        $alreadyPlannedRows = $this->getDataAlreadyPlannedOrErrors();

        if (empty($alreadyPlannedRows)) {
            return;
        }

        foreach ($alreadyPlannedRows as $planned) {
            $rows[] = [
                'id' => $planned['id'],
                'transactionId' => $planned['transaction_id'],
                'aborted_by' => match ($planned['status']) {
                    Withdrawal::STATUS_PLANNED_ABORT => Str::emailToLdap($planned['updated_by_email']),
                    Withdrawal::STATUS_SYNC_ERROR => 'sync error',
                    default => false,
                },
                'createdAt' => $planned['created_at'],
                'plannedAt' => $planned['planned_at'] ? date(self::DATETIME_FORMAT, strtotime($planned['planned_at'])) : null,
                'sum' => $this->formatMoney($planned['sum'], $planned['currency']),
            ];
        }

        usort($rows, static fn($a, $b) => $a['plannedAt'] <=> $b['plannedAt']);
    }

    private function fillFormValues(): void
    {
        $plannedWithdrawals = $this->getWithdrawalsToPlan();

        $this->combinationsList = $this->getSumCombinations(array_map(static fn($row) => [
            'amount_orig' => $row['sum'],
            'transaction_id' => $row['transaction_id'],
        ], $plannedWithdrawals));

        if (!isset($this->plannedAt)) {
            $predictedWithdrawAt = (new Query($this->db))
                ->select("DATE_PART('epoch', MIN(us.created_at + wp.predicted_delay))")
                ->from(['wp' => WithdrawalPredictions::TABLE_NAME])
                ->leftJoin(['us' => UserTransactions::TABLE_NAME], [
                    'AND',
                    'us.site_id = wp.site_id AND us.user_id = wp.user_id',
                    [
                        'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                        'us.dir' => UserTransaction::DIR_OUT,
                        'us.status' => UserTransaction::STATUS_NEW,
                    ]
                ])
                ->where([
                    'wp.site_id' => $this->siteId,
                    'wp.user_id' => $this->userId,
                ])
                ->scalar();
            $this->plannedAt = date(self::DATETIME_FORMAT, (int)max($predictedWithdrawAt, time() + 60));
        }
    }

    private function getSumCombinations(array $combinations): array
    {
        if (empty($combinations)) {
            return [];
        }

        $result = [];
        $combinations = $this->pm->getSumCombinations($combinations);
        foreach ($combinations as $amount => $ids) {
            $result[] = [
                'id' => implode(',', $ids),
                'name' => $this->formatMoney((string)$amount)
            ];
        }

        return $result;
    }

    private function getWithdrawalsToPlan(): array
    {
        static $data;
        return $data ??= $this->getWithdrawalsBaseQuery(['w.status' => [Withdrawal::STATUS_NEW, Withdrawal::STATUS_PLANNED, Withdrawal::STATUS_SYNCED]])
            ->andWhere(['w.id' => null])
            ->all();
    }

    private function getDataAlreadyPlannedOrErrors(): array
    {
        static $data;
        return $data ??= $this->getWithdrawalsBaseQuery()
            ->andWhere(['w.status' => [Withdrawal::STATUS_PLANNED, Withdrawal::STATUS_PLANNED_ABORT, Withdrawal::STATUS_SYNC_ERROR]])
            ->all();
    }

    private function getWithdrawalsBaseQuery(string | array ...$joinCondition): Query
    {
        return (new Query($this->db))
            ->select([
                "w.id",
                'status' => 'w.status',
                'updated_by_email' => 'e.email',
                'us.transaction_id',
                'sum' => 'amount_orig',
                'us.created_at',
                'us.currency',
                'w.planned_at',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['w' => Withdrawals::TABLE_NAME], [
                'AND',
                'w.site_id = us.site_id AND w.transaction_id = us.transaction_id',
                ...$joinCondition,
            ])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'w.updated_by = e.employee_id')
            ->where([
                'us.site_id' => $this->siteId,
                'us.user_id' => $this->userId,
                'us.status' => UserTransaction::STATUS_NEW,
                'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                'us.dir' => UserTransaction::DIR_OUT,
            ]);
    }

    public static function sumOrCountLimitValidator(mixed $value, self $form, array $context): ?string
    {
        if (empty($context['limitCount']) xor empty($context['limitSum'])) {
            return null;
        }

        return 'specify sum or count';
    }
}
