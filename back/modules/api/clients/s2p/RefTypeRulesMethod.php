<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\s2p;

use app\back\entities\RefTypeRule;
use app\back\repositories\RefTypeRules;
use Yiisoft\Db\Query\Query;

class RefTypeRulesMethod extends S2pGetMethod
{
    public function run(): iterable
    {
        $query = (new Query($this->db))
            ->select([
                'prefix',
                'ref_type',
                'created_at',
            ])
            ->from(['rtr' => RefTypeRules::TABLE_NAME]);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure
    {
        return static function ($row) {
            $row['ref_type_name'] = RefTypeRule::getRefTypeName($row['ref_type']);
            $row['ref_type_group'] = RefTypeRule::getGroupByRefType($row['ref_type']);
            return $row;
        };
    }
}
