<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\refTypes;

use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\components\SessionMessages;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\RefTypeRule;
use app\back\repositories\RefTypeRules;
use Yiisoft\Db\Expression\Expression;

class RefTypesCreateForm
{
    use FormGrid;

    #[StringMultilineValidator(0, 1000)]
    public string $values;

    #[IntInArrayValidator(RefTypeRule::OPERATORS)]
    public int $valuesOperator = RefTypeRule::OPERATOR_PREFIX;

    #[IntInArrayValidator(RefTypeRule::REF_TYPES)]
    public int $refType;

    public function __construct(
        private readonly RefTypeRules $refTypeRules,
        private readonly SessionMessages $messages
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textAreaCell(12, 'values', 'Values', [
                    'operators' => Arr::assocToIdName(RefTypeRule::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
            [
                $this->selectCell(12, 'refType', 'Ref type', [
                    'list' => Arr::assocToIdName(RefTypeRule::REF_TYPES),
                    'multiple' => false,
                ]),
            ],
            [
                $this->submitCell(6, 'Add', [
                    'buttonIcon' => 'icn-plus',
                ]),
            ],
        ];
    }

    public function create(int $employeeId): void
    {
        $values = Str::explodeText($this->values);
        $processed = 0;
        foreach ($values as $value) {
            $processed += $this->refTypeRules->upsert(['value'], [
                'value' => $value,
                'value_operator' => $this->valuesOperator,
                'ref_type' => $this->refType,
                'created_by' => $employeeId,
            ], [
                'created_at' => new Expression('NOW()'),
            ]);
        }
        $message = $processed === 0 ? 'Nothing changed' : "Successfully processed ($processed)";
        $this->messages->success($message);
    }
}
