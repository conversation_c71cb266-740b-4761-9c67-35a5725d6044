<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\refTypes;

use app\back\components\helpers\Arr;
use app\back\components\RichTable;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\RefTypeRule;
use app\back\repositories\RefTypeRules;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class RefTypesListForm
{
    use RichTable;

    #[StringValidator(0, 500)]
    public ?string $value = '';

    #[IntArrayValidator(RefTypeRule::REF_TYPES)]
    public array $refType = [];

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
        $this->pageSize = 500;
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'ID', 'code' => 'id'],
            ['name' => 'Value', 'code' => 'value'],
            ['name' => 'Value operator', 'code' => 'value_operator'],
            ['name' => 'Ref type', 'code' => 'ref_type_name'],
            ['name' => 'Created', 'code' => 'created_at'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(2, 'value', 'Value', ['focusOnMount' => true]),
                $this->listCell(10, 'refType', 'Ref type', [
                    'list' => Arr::assocToIdName(RefTypeRule::REF_TYPES),
                ]),
            ],
        ];
    }

    public function data(): array
    {
        $rows = $this->composeQuery()
            ->select([
                'id' => 'rtr.id',
                'value' => 'rtr.value',
                'value_operator' => 'rtr.value_operator',
                'ref_type' => 'rtr.ref_type',
                'created_at' => 'rtr.created_at',
            ])
            ->orderBy(['rtr.id' => SORT_DESC])
            ->offset($this->getOffset())
            ->limit($this->getLimit())
            ->all();

        foreach ($rows as &$row) {
            $row['ref_type_name'] = RefTypeRule::getRefTypeName($row['ref_type']);
            $row['value_operator'] = RefTypeRule::OPERATORS[$row['value_operator']];
        }

        return $rows;
    }

    protected function total(): int
    {
        return (new Query($this->db))->from($this->composeQuery())->count();
    }

    private function composeQuery(): Query
    {
        $query = (new Query($this->db))
            ->from(['rtr' => RefTypeRules::TABLE_NAME]);

        if ($this->value) {
            $query->andFilterWhere(['ILIKE', 'rtr.value', trim($this->value) . '%', null]);
        }

        if ($this->refType) {
            $query->andFilterWhere(['rtr.ref_type' => $this->refType]);
        }

        return $query;
    }
}
