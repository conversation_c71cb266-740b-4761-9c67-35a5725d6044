<?php

declare(strict_types=1);

namespace app\back\modules\back\usersStatusesVipThresholds;

use app\back\components\AllowedLists;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Db;
use app\back\components\RichTable;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\BooleanArrayValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\entities\Country;
use app\back\entities\User;
use app\back\entities\UserStatusVipThreshold;
use app\back\repositories\Countries;
use app\back\repositories\Employees;
use app\back\repositories\Sites;
use app\back\repositories\UserStatusVipThresholds;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersStatusesVipThresholdsFilterForm
{
    use RichTable;

    public const string ALL_OTHER = 'All other';
    public const string ANY = 'Any';

    #[AllowedSitesValidator]
    public array $siteId = [];
    #[StringArrayValidator(2, 2)]
    public ?array $country = [];
    #[IntArrayValidator(User::STATUSES)]
    public ?array $status = [];
    #[IntArrayValidator(User::ACTIVE_STATUSES)]
    public ?array $activeStatus = [];
    #[BooleanArrayValidator]
    public ?array $isActive = [];
    #[BooleanArrayValidator]
    public ?array $isUp = [];
    #[IntArrayValidator(UserStatusVipThreshold::CHECK_PERIODS)]
    public ?array $checkPeriod = [];

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly ConnectionInterface $db,
        private readonly Employees $employeesRepo,
        private readonly Countries $countriesRepo,
        private readonly Sites $sitesRepo,
    ) {
        $this->sort = 'id';
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(2, 'siteId'),
                $this->selectCell(1, 'country', 'Country', [
                    'list' => Arr::assocToIdName($this->countriesRepo->getNames()),
                    'multiple' => true,
                ]),
                $this->selectCell(1, 'status', 'Status', [
                    'list' => Arr::assocToIdName(User::STATUSES),
                    'multiple' => true,
                ]),
                $this->selectCell(1, 'activeStatus', 'Active status', [
                    'list' => Arr::assocToIdName(User::ACTIVE_STATUSES),
                    'multiple' => true,
                ]),
                $this->selectBooleanCell(1, 'isUp', 'Is up'),
                $this->selectBooleanCell(1, 'isActive', 'Is active'),
                $this->listCell(1, 'checkPeriod', 'Check period', [
                    'list' => Arr::assocToIdName(UserStatusVipThreshold::CHECK_PERIODS),
                    'multiple' => true,
                ]),
            ],
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Id', 'code' => 'id', 'sortable' => true],
            ['name' => 'Site', 'code' => 'site', 'sortable' => true, 'sortExpr' => 'site_id'],
            ['name' => 'Country', 'code' => 'country', 'sortable' => true],
            ['name' => 'Status', 'code' => 'status', 'sortable' => true],
            ['name' => 'Active status', 'code' => 'activeStatus', 'sortable' => true],
            ['name' => 'Is up', 'code' => 'isUp', 'slotName' => 'isUp'],
            ['name' => 'Is active', 'code' => 'isActive', 'slotName' => 'isActive'],
            ['name' => 'Amount', 'code' => 'amount', 'slotName' => 'amount'],
            ['name' => 'Currency', 'code' => 'currency'],
            ['name' => 'Period', 'code' => 'period', 'slotName' => 'period'],
            ['name' => 'Check period', 'code' => 'checkPeriod', 'slotName' => 'checkPeriod'],
            ['name' => 'Updated by', 'code' => 'updatedBy'],
            ['name' => 'Updated at', 'code' => 'updatedAt', 'sortable' => true],
            ['name' => 'Delete', 'code' => 'delete', 'slotName' => 'delete'],
        ];
    }

    public function data(): array
    {
        $siteIdsWithOthers = empty($this->siteId) ? [] : [...$this->siteId, UserStatusVipThreshold::SITE_ID_ANY];
        $countryCodesWithOthers = empty($this->country) ? [] : [...$this->country, Country::DEFAULT];

        $rows = (new Query($this->db))
            ->select([
                'id',
                'countryCode' => 'country',
                'siteId' => 'site_id',
                'status',
                'activeStatus' => 'active_status',
                'amount' => 'round(amount)',
                'updatedBy' => 'updated_by',
                'updatedAt' => 'updated_at',
                'isUp' => 'is_up',
                'isActive' => 'is_active',
                'period' => Db::intervalToIso8601('period'),
                'checkPeriod' => 'check_period',
            ])
            ->from(['usvt' => UserStatusVipThresholds::TABLE_NAME])
            ->andFilterWhere(['usvt.site_id' => $siteIdsWithOthers])
            ->andFilterWhere(['usvt.country' => $countryCodesWithOthers])
            ->andFilterWhere(['usvt.status' => $this->status])
            ->andFilterWhere(['usvt.active_status' => $this->activeStatus])
            ->andFilterWhere(['usvt.is_up' => $this->isUp])
            ->andFilterWhere(['usvt.is_active' => $this->isActive])
            ->andFilterWhere(['usvt.check_period' => $this->checkPeriod])
            ->orderBy($this->getOrderMap())
            ->offset($this->getOffset())
            ->limit($this->getLimit())
            ->all();

        foreach ($rows as &$row) {
            $row['site'] = $row['siteId'] === UserStatusVipThreshold::SITE_ID_ANY ? self::ALL_OTHER : $this->sitesRepo->getNameById($row['siteId']);
            $row['updatedBy'] = $this->employeesRepo->getNameById($row['updatedBy']);
            $row['country'] = $row['countryCode'] === Country::DEFAULT ? self::ALL_OTHER : $this->countriesRepo->getNameById($row['countryCode']);
            $row['currency'] = Country::getCurrencyByCountryCode2($row['countryCode']);
            $row['status'] = User::getStatusById($row['status']);
            $row['activeStatus'] = User::getActiveStatusById($row['activeStatus']);
            $row['deleteDisabled'] = $row['countryCode'] === Country::DEFAULT && $row['siteId'] === UserStatusVipThreshold::SITE_ID_ANY;
        }

        return $rows;
    }

    protected function total(): int
    {
        $total = (new Query($this->db))
            ->select('COUNT(*)')
            ->from(UserStatusVipThresholds::TABLE_NAME);

        return (int) $total->scalar();
    }

    protected function additionalResponse(): array
    {
        return [
            'availablePeriods' => Arr::columnToIdName(UserStatusVipThreshold::PERIODS),
            'availableCheckPeriods' => Arr::assocToIdName(UserStatusVipThreshold::CHECK_PERIODS),
        ];
    }
}
