<?php

declare(strict_types=1);

namespace app\back\modules\back\usersStatusesVipThresholds;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\InvalidException;
use app\back\components\exceptions\UserException;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntervalValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\Country;
use app\back\entities\User;
use app\back\entities\UserStatusVipThreshold;
use app\back\repositories\Countries;
use app\back\repositories\UserStatusVipThresholds;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Exception\IntegrityException;

class UsersStatusesVipThresholdsAddForm
{
    use FormGrid;

    public const array COLUMNS_DB_MAP = [
        'country' => 'country',
        'site_id' => 'siteId',
        'status' => 'status',
        'active_status' => 'activeStatus',
        'amount' => 'amount',
        'is_up' => 'isUp',
        'is_active' => 'isActive',
        'period' => 'period',
        'check_period' => 'checkPeriod',
    ];

    #[StringValidator(2, 2)]
    public ?string $country = null;
    #[IntValidator(-1)]
    public ?int $siteId = null;
    #[IntInArrayValidator(User::STATUSES)]
    public int $status;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public int $activeStatus;
    #[MoneyValidator]
    public string $amount;
    #[BooleanValidator]
    public bool $isUp = true;
    #[StringInArrayValidator(UserStatusVipThreshold::PERIODS, true)]
    #[IntervalValidator(false)]
    public \DateInterval $period;
    #[IntInArrayValidator(UserStatusVipThreshold::CHECK_PERIODS)]
    public int $checkPeriod = UserStatusVipThreshold::CHECK_PERIOD_MONTH;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly ConnectionInterface $db,
        private readonly UserStatusVipThresholds $thresholdsRepo,
        private readonly Countries $countriesRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectSiteCell(6, 'siteId', 'Site', [
                    'multiple' => false,
                ]),
                $this->selectCell(6, 'country', 'Country', [
                    'list' => Arr::assocToIdName($this->countriesRepo->getNames()),
                    'multiple' => false,
                ]),
            ], [
                $this->selectCell(6, 'status', 'Status', [
                    'list' => Arr::assocToIdName(Arr::leaveOnlyKeys(User::STATUSES, User::VIP_STATUSES)),
                    'multiple' => false,
                ]),
                $this->selectCell(6, 'activeStatus', 'Active status', [
                    'list' => Arr::assocToIdName(User::ACTIVE_STATUSES),
                    'multiple' => false,
                ]),
            ],
            [
                $this->selectBooleanCell(4, 'isUp', 'Is up'),
                $this->textInputCell(4, 'amount', 'Amount', [
                    'hint' => 'EUR by default (not mapped)',
                ]),
                $this->selectCell(4, 'period', 'Period', [
                    'list' => Arr::columnToIdName(UserStatusVipThreshold::PERIODS),
                    'multiple' => false,
                ]),
            ],
            [
                $this->selectCell(4, 'checkPeriod', 'Check period', [
                    'list' => Arr::assocToIdName(UserStatusVipThreshold::CHECK_PERIODS),
                ]),
                $this->textStaticCell(4, ''),
                $this->submitCell(4, 'Submit'),
            ],
        ];
    }

    public function checkGeneralSiteIdThreshold(UserStatusVipThreshold $entity): void
    {
        if ($entity->site_id !== UserStatusVipThreshold::SITE_ID_ANY) {
            $generalCase = $this->thresholdsRepo->findOne([
                'site_id' => UserStatusVipThreshold::SITE_ID_ANY,
                'country' => $entity->country,
                'status' => $entity->status,
                'active_status' => $entity->active_status,
                'is_up' => $entity->is_up,
                'period' => $entity->period,
            ]);

            if ($generalCase === null) {
                throw new InvalidException('General case (with same filters) must be created before case with specific site');
            }
        }
    }

    public function checkGeneralCountryThreshold(UserStatusVipThreshold $entity): void
    {
        if ($entity->country !== Country::DEFAULT) {
            $generalCase = $this->thresholdsRepo->findOne([
                'site_id' => $entity->site_id,
                'country' => Country::DEFAULT,
                'status' => $entity->status,
                'active_status' => $entity->active_status,
                'is_up' => $entity->is_up,
                'period' => $entity->period,
            ]);

            if ($generalCase === null) {
                throw new InvalidException('General case (with same filters) must be created before case with specific country');
            }
        }
    }

    public function save(): int
    {
        $entity = new UserStatusVipThreshold();
        foreach (static::COLUMNS_DB_MAP as $entityName => $formName) {
            if (!property_exists($this, $formName)) {
                continue;
            }

            $v = $this->$formName;
            if ($v === null) {
                $v = match ($formName) {
                    'country' => Country::DEFAULT,
                    'siteId' => UserStatusVipThreshold::SITE_ID_ANY,
                    default => $v,
                };
            }
            $entity->$entityName = $v;
        }

        $entity->updated_by = $this->auth->employeeId();

        $this->checkGeneralCountryThreshold($entity); // Country specific must be checked first
        $this->checkGeneralSiteIdThreshold($entity);

        try {
            $this->thresholdsRepo->insert($entity);
        } catch (IntegrityException $e) {
            throw new UserException('Rule with same (conflicting) Site, Country, Status, Active status, Is up and Period already exists');
        }


        return $entity->id;
    }
}
