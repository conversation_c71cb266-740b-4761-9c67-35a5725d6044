<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\entities\Country;
use app\back\entities\Rate;
use app\back\entities\UserStatusVipThreshold;
use app\back\entities\UserTransaction;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Rates;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserStatusVipThresholds;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersStatusesMonthlyTask extends BaseTask
{
    use UsersStatusesUpdateTrait;

    public function __construct(
        protected readonly UserStatusVipThresholds $vipThresholds,
        private readonly ConnectionInterface $db,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Users $usersRepo,
    ) {
    }

    public function process(): void
    {
        $siteId = $this->siteIdResolver->siteId();
        $processOrders = [
            [UserStatusVipThreshold::STATUSES_UPGRADE_PROCESS_ORDER, true],
            [UserStatusVipThreshold::STATUSES_DOWNGRADE_PROCESS_ORDER, false],
        ];

        foreach ($processOrders as [$processOrder, $isUp]) {
            $thresholds = Arr::groupBy($this->getThresholds($isUp), ['status', 'active_status', 'site_id'], 'country');
            $steps = $this->getSteps($processOrder, $thresholds, $siteId);
            $this->updateUsersFromSteps($steps);
        }
    }

    private function getThresholds(bool $isUp): iterable
    {
        return $this->vipThresholds->findEach([
            'site_id' => [$this->siteIdResolver->siteId(), UserStatusVipThreshold::SITE_ID_ANY],
            'is_up' => $isUp,
            'check_period' => UserStatusVipThreshold::CHECK_PERIOD_MONTH,
            'is_active' => true,
        ]);
    }

    private function getSteps(array $fromToMap, array $thresholds, int $siteId): array
    {
        $steps = [];
        foreach ($fromToMap as $statusToProcess => $activeStatusesForProcess) {
            if (!array_key_exists($statusToProcess, $thresholds)) {
                continue;
            }

            $thresholdsActiveStatuses = $thresholds[$statusToProcess];
            foreach ($activeStatusesForProcess as $activeStatusForProcess => $fromStatuses) {
                if (!array_key_exists($activeStatusForProcess, $thresholdsActiveStatuses)) {
                    continue;
                }

                $thresholdsSitesCountries = $thresholdsActiveStatuses[$activeStatusForProcess];
                // Check if site specific rule exists
                if (array_key_exists($siteId, $thresholdsSitesCountries)) {
                    $thresholdsCountries = $thresholdsSitesCountries[$siteId];
                } else {
                    if (!array_key_exists(UserStatusVipThreshold::SITE_ID_ANY, $thresholdsSitesCountries)) {
                        $this->log->warning("No general rule for status $statusToProcess and active status $activeStatusForProcess");
                        continue;
                    }
                    $thresholdsCountries = $thresholdsSitesCountries[UserStatusVipThreshold::SITE_ID_ANY];
                }

                $fromStatusesUpdateCondition = $this->getFromStatusesUpdateCondition($fromStatuses);

                // Check if country specific rule exists
                $notCountries = [];
                foreach ($thresholdsCountries as $country => $threshold) {
                    /** @var UserStatusVipThreshold $threshold */
                    if ($country !== Country::DEFAULT) {
                        $notCountries[] = $country;
                        $steps[] = $this->addStep($threshold, $fromStatusesUpdateCondition, $country);
                    }
                }

                if (array_key_exists(Country::DEFAULT, $thresholdsCountries)) { // General rule must be added last
                    $threshold = $thresholdsCountries[Country::DEFAULT];
                    $steps[] = $this->addStep($threshold, $fromStatusesUpdateCondition, Country::DEFAULT, $notCountries);
                }
            }
        }

        return array_filter($steps); // Remove steps that are not on
    }

    private function getFromStatusesUpdateCondition(mixed $fromStatuses): array
    {
        $result = ['OR'];
        foreach ($fromStatuses as $fromStatusAndMaybeActiveStatus) {
            if (count($fromStatusAndMaybeActiveStatus) === 2) {
                $result[] = [
                    'u.status' => $fromStatusAndMaybeActiveStatus[0],
                    'u.active_status' => $fromStatusAndMaybeActiveStatus[1],
                ];
            } else {
                $result[] = ['u.status' => $fromStatusAndMaybeActiveStatus[0]];
            }
        }
        return $result;
    }

    private function addStep(UserStatusVipThreshold $threshold, array $fromStatusesUpdateCondition, string $country, array $notCountries = []): array
    {
        $currency = Country::getCurrencyByCountryCode2($country);
        $amountColumn = match ($currency) {
            Rate::USD => 'COALESCE(SUM(us.amount_usd), 0)',
            Rate::EUR => 'COALESCE(SUM(us.amount_eur), 0)',
            Rate::RUB => 'COALESCE(SUM(us.amount_rub), 0)',
            default => 'COALESCE(SUM(us.amount_usd * r.rate), 0)',
        };

        if ($country === Country::DEFAULT) {
            if ($notCountries) {
                $countriesWhere = ['OR', ['NOT IN', 'u.country', $notCountries], ['u.country' => null]];
            } else {
                $countriesWhere = [];
            }
        } else {
            $countriesWhere = ['u.country' => $country];
        }

        $transactionsCheckTo = date('Y-m-01', strtotime($this->from));
        $transactionsCheckFrom = (new \DateTimeImmutable($transactionsCheckTo))->sub($threshold->period)->format(DateHelper::DATE_FORMAT_PHP);

        $step = [
            'id' => $threshold->id,
            'currency' => $currency,
            'update' => [
                'status' => $threshold->status,
                'active_status' => $threshold->active_status,
            ],
            'where' => ['AND',
                $countriesWhere,
                $fromStatusesUpdateCondition,
            ],
            'conditionsStats' => ['AND',
                ['<', 'us.updated_at', $transactionsCheckTo],
                ['>=', 'us.updated_at', $transactionsCheckFrom]
            ],
        ];


        if ($threshold->is_up) {
            $step['having'] = ['>=', $amountColumn, $threshold->amount];
        } else {
            $step['having'] = ['<', $amountColumn, $threshold->amount];
            $step['where'][] = ['<', 'u.status_updated_at', $transactionsCheckFrom];
        }
        return $step;
    }

    private function updateUsersFromSteps(array $steps): void
    {
        foreach ($steps as $step) {
            $users = $this->findUsers($step);
            $this->log->debug("Threshold id: {$step['id']}, affected: " . count($users));
            $this->updateUsersStatus($users, $step['update']['status'], $step['update']['active_status']);
        }
    }

    private function findUsers(array $step): array
    {
        $rateQuery = (new Query($this->db))
            ->select(['rate'])
            ->from(['r' => Rates::TABLE_NAME])
            ->where('r.code = :currency AND r.date <= us.created_at')
            ->orderBy(['date' => SORT_DESC])
            ->limit(1);

        $query = (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->leftJoin(['us' => UserTransactions::TABLE_NAME], [
                'AND',
                'us.site_id = u.site_id',
                'us.user_id = u.user_id',
                [
                    'us.op_id' => UserTransaction::OP_IN,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                ],
                $step['conditionsStats'],
            ])
            ->join('LEFT JOIN LATERAL', ['r' => $rateQuery], 'TRUE')
            ->where([
                'AND',
                ['u.site_id' => $this->siteIdResolver->siteId()],
                ['IS DISTINCT FROM', 'u.is_manual_status', true],
                $step['where'],
            ])
            ->groupBy(['u.site_id', 'u.user_id'])
            ->having($step['having'])
            ->addParams(['currency' => $step['currency']]);

        return $query->all();
    }
}
