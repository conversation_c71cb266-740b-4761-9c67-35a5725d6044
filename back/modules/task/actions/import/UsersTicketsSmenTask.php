<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Str;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\modules\task\actions\TaskWithFromToRequest;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTicketsLogs;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersTicketsSmenTask extends ImportTask
{
    use TaskWithFromToRequest;

    private const array ALLOWED_EXTENSIONS_MAP = [
        'jpg' => 'jpg',
        'jpeg' => 'jpg',
        'jfif' => 'jpg',
        'png' => 'png',
        'pdf' => 'pdf',
    ];

    /** Product could only create (need_approve) or close just created ticket (decline) - @bitter **/
    private const array ALLOWED_STATUSES = [
        UserTicket::STATUS_NEED_APPROVE,
        UserTicket::STATUS_DECLINED,
    ];

    private readonly array $ticketStatuses;
    private readonly array $ticketTypes;
    private readonly string $productSource;

    private array $newTickets = [];
    private array $ticketsFiles = [];

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketsLogs $userTicketLogsRepo,
        private readonly UserTicketFiles $userTicketsFilesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
        $this->ticketStatuses = array_flip(array_map(static fn($r) => Str::wordsToSnake($r), UserTicket::STATUSES));
        $this->ticketTypes = array_flip(array_map(static fn($r) => Str::wordsToSnake($r), UserTicket::TYPES));
        $this->productSource = Str::wordsToSnake(UserTicket::SOURCES[UserTicket::SOURCE_PRODUCT]);
    }

    protected function repository(): BaseRepository
    {
        return $this->userTicketsRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        if (!isset($this->ticketStatuses[$row['status']])) {
            $this->log->debug("Unknown ticket status '{$row['status']}' in ticket {$row['product_ticket_id']}");
            return false;
        }

        if (!isset($this->ticketTypes[$row['type']])) {
            $this->log->debug("Unknown ticket type '{$row['type']}' in ticket {$row['product_ticket_id']}");
            return false;
        }

        if ($row['source'] !== $this->productSource) {
            $this->log->debug("Non-importable ticket source '{$row['source']}' in ticket {$row['product_ticket_id']}");
            return false;
        }

        $status = $this->ticketStatuses[$row['status']];

        if (!in_array($status, self::ALLOWED_STATUSES, true)) {
            $this->log->debug("Non-importable ticket status '" . UserTicket::STATUSES[$status] . "' in ticket {$row['product_ticket_id']}");
            return false;
        }

        $row['site_id'] = $this->siteIdResolver->siteId();

        $row['status'] = $status;

        $row['type'] = $this->ticketTypes[$row['type']];

        $row['source'] = UserTicket::SOURCE_PRODUCT;

        $this->parseFiles($row);

        unset($row['files']);

        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        $productTicketsIds = array_column($rows, 'product_ticket_id');

        $this->keepDataForExistingTickets($rows, $productTicketsIds);

        $affected = $this->userTicketsRepo->batchUpsert($rows, [], '(site_id, product_ticket_id) WHERE (product_ticket_id IS NOT NULL)');

        $existingTicketsIds = $this->getColumnsFromExistingTickets(['id'], $productTicketsIds); // get id of existing + newly inserted tickets

        $logs = $this->generateLogRecords($rows, $existingTicketsIds);

        $affected += $this->userTicketLogsRepo->batchUpsert($logs, [], '(ticket_id, status, source, created_at)');

        foreach ($this->ticketsFiles as &$file) {
            if (array_key_exists($file['product_ticket_id'], $existingTicketsIds)) {
                $file['ticket_id'] = $existingTicketsIds[$file['product_ticket_id']]['id'];
                unset($file['product_ticket_id']);
            }
        }

        $affected += $this->userTicketsFilesRepo->batchUpsert($this->ticketsFiles, [], '(ticket_id, product_file_key)');

        return $affected;
    }

    private function keepDataForExistingTickets(array $rows, array $productTicketsIds): array
    {
        $existingUpdatedAts = $this->getColumnsFromExistingTickets(['created_at', 'updated_at', 'source'], $productTicketsIds);

        foreach ($rows as &$row) {
            if (!isset($existingUpdatedAts[$row['product_ticket_id']])) {
                $this->newTickets[] = $row['product_ticket_id'];
                continue;
            }

            $analyticTicket = $existingUpdatedAts[$row['product_ticket_id']];

            $row['created_at'] = $analyticTicket['created_at'];

            if (strtotime($row['updated_at']->format(DateHelper::DATETIME_FORMAT_PHP)) < strtotime($analyticTicket['updated_at'])) {
                $row['updated_at'] = $analyticTicket['updated_at']; // don't overwrite updated_at if it newer then imported
                $row['source'] = $analyticTicket['source'];
            }
        }

        return $rows;
    }

    private function generateLogRecords(array $rows, array $existingTickets): array
    {
        $logs = [];
        foreach ($rows as $row) {
            $log = [
                'ticket_id' => $existingTickets[$row['product_ticket_id']]['id'],
                'source' => $row['source'],
            ];

            if (in_array($row['product_ticket_id'], $this->newTickets, true)) {
                $logs[] = array_merge($log, ['created_at' => $row['created_at'], 'status' => UserTicket::STATUS_NEED_APPROVE]);
            }

            if ($row['status'] === UserTicket::STATUS_DECLINED) {
                $logs[] = array_merge($log, ['created_at' => $row['updated_at'], 'status' => UserTicket::STATUS_DECLINED]);
            }
        }

        return $logs;
    }

    private function getColumnsFromExistingTickets(array $columns, array $productTicketIds): array
    {
        $result = (new Query($this->db))
            ->select(['product_ticket_id', ...$columns])
            ->from(UserTickets::TABLE_NAME)
            ->where([
                'site_id' => $this->siteIdResolver->siteId(),
                'product_ticket_id' => $productTicketIds,
            ])
            ->all();

        return Arr::index($result, 'product_ticket_id');
    }

    private function parseFiles(array $row): void
    {
        foreach ($row['files'] as $file) {
            $extension = strtolower(pathinfo($file['originalName'], PATHINFO_EXTENSION));
            if (!array_key_exists($extension, self::ALLOWED_EXTENSIONS_MAP)) {
                continue;
            }
            $this->ticketsFiles[] = [
                'product_file_key' => $file['hash'],
                'product_ticket_id' => $row['product_ticket_id'],
                'extension' => self::ALLOWED_EXTENSIONS_MAP[$extension],
                'original_name' => $file['originalName'],
                'created_at' => $row['created_at'],
                'sync_status' => UserTicketFile::SYNC_TO_DOWNLOAD,
                'source' => UserTicket::SOURCE_PRODUCT,
            ];
        }
    }
}
