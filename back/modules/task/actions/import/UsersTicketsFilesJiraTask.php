<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\entities\UserTicket;
use app\back\modules\task\requests\JiraFilesRequest;

/** @property JiraFilesRequest $request */
class UsersTicketsFilesJiraTask extends UsersTicketsFilesBaseTask
{
    protected const string REMOTE_FILE_ID_COLUMN = 'jira_file_id';

    protected function getFilesToDownload(): array
    {
        return $this->baseFilesToDownloadQuery()
            ->addSelect([
                'jiraFileName' => 'utf.original_name',
                'jiraFileId' => 'utf.' . self::REMOTE_FILE_ID_COLUMN,
            ])
            ->andWhere(['utf.source' => UserTicket::SOURCE_JIRA])
            ->all();
    }
}
