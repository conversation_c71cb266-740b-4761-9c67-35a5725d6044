<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import;

use app\back\entities\UserTicket;
use app\back\modules\task\requests\SmenTicketsFileRequest;
use app\back\repositories\UserTickets;

/** @property SmenTicketsFileRequest $request */
class UsersTicketsFilesSmenTask extends UsersTicketsFilesBaseTask
{
    protected const string REMOTE_FILE_ID_COLUMN = 'product_file_key';

    protected function getFilesToDownload(): array
    {
        return $this->baseFilesToDownloadQuery()
            ->addSelect([
                'productTicketId' => 'ut.product_ticket_id',
                'productFileKey' => 'utf.' . self::REMOTE_FILE_ID_COLUMN,
            ])
            ->innerJoin(['ut' => UserTickets::TABLE_NAME], 'ut.id = utf.ticket_id')
            ->andWhere(['utf.source' => UserTicket::SOURCE_PRODUCT])
            ->all();
    }
}
