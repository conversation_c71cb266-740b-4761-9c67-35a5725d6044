<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\UserTicketFile;
use Symfony\Contracts\HttpClient\ResponseInterface;

class JiraFilesRequest extends BaseRequest
{
    use RequestWithHttpClient;

    public string $authKey;

    public int $jiraFileId;
    public string $jiraFileName;
    public int $fileId;

    protected function fetchData(): iterable
    {
        $url = $this->buildUrl([':jiraFileId' => $this->jiraFileId, ':jiraFileName' => $this->jiraFileName]);

        $response = $this->createHttpClient()->get($url, [
            'auth_basic' => $this->authKey,
        ]);

        return $this->responseToData($response);
    }

    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $result = [
            'jira_file_id' => $this->jiraFileId,
            'fileId' => $this->fileId,
            'fileContent' => null,
        ];

        try {
            $this->handleStatusCode($response);
            $result['fileContent'] = $response->getContent(false);
            $result['sync_status'] = UserTicketFile::SYNC_SUCCESS;
        } catch (\Throwable $e) {
            $this->log->error("Failed to download file with jira id {$result['jira_file_id']}: " . $e->getMessage());
            $result['sync_status'] = UserTicketFile::SYNC_FAILED;
        }

        return $result;
    }
}
