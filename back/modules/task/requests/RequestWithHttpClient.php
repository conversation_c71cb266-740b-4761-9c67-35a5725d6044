<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\HttpClient;
use app\back\modules\task\requests\exceptions\AuthException;
use app\back\modules\task\requests\exceptions\ClientException;
use app\back\modules\task\requests\exceptions\HttpException;
use app\back\modules\task\requests\exceptions\ServerException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

trait RequestWithHttpClient
{
    public string $host;
    public string $url;
    public int $curlTimeout = 60;
    public bool $dumpRaw = false;

    protected function handleStatusCode(ResponseInterface $response): void
    {
        $code = $response->getStatusCode();

        $exceptionClass = match ($code) {
            // 2xx
            Response::HTTP_OK,
            Response::HTTP_NO_CONTENT => null,

            // 4xx
            Response::HTTP_FORBIDDEN,
            Response::HTTP_UNAUTHORIZED => AuthException::class,

            Response::HTTP_NOT_FOUND,
            Response::HTTP_BAD_REQUEST => ClientException::class,

            // 5xx
            Response::HTTP_INTERNAL_SERVER_ERROR,
            Response::HTTP_BAD_GATEWAY,
            Response::HTTP_GATEWAY_TIMEOUT => ServerException::class,

            default => HttpException::class,
        };

        if ($exceptionClass !== null) {
            throw new $exceptionClass($response);
        }
    }

    final protected function createHttpClient(array $config = []): HttpClient
    {
        $config = array_merge([
            'timeout' => $this->curlTimeout,
            'headers' => ['User-Agent' => 'Analytic cURL'],
        ], $config);

        $client = new HttpClient($config);
        $client->dumpRaw = $this->dumpRaw;

        $client->setLogger($this->log);

        return $client;
    }

    protected function buildUrl(array $params = []): string
    {
        return $this->host . '/' . $this->replaceUrlParams($this->url, $params);
    }

    protected function replaceUrlParams(string $url, array $params = []): string
    {
        return strtr($url, array_map('rawurlencode', $params));
    }

    final protected function addQueryParams(string $url, array $queryParams = []): string
    {
        if (str_contains($url, '?')) {
            $queryString = parse_url($url, PHP_URL_QUERY);
            parse_str($queryString, $query);
            $url = str_replace("?$queryString", '', $url);
            $queryParams = array_merge($query, $queryParams);
        }

        $queryPart = empty($queryParams) ? '' : ('?' . http_build_query($queryParams));

        return $url . $queryPart;
    }

    final protected function isProxyProblem(TransportExceptionInterface $e): bool
    {
        $m = $e->getMessage();

        if (str_starts_with($m, "Can't complete SOCKS5 connection")) {
            return true;
        }

        if (preg_match('#Failed to connect to \d+\.\d+\.\d+\.\d+ port \d+: (?:Connection timed out|Network is unreachable)#', $m)) {
            return true;
        }

        return false;
    }
}
