<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\UserTicketFile;
use Symfony\Contracts\HttpClient\ResponseInterface;

class SmenTicketsFileRequest extends SmenRequest
{
    public int $fileId;
    public int $productTicketId;
    public string $productFileKey;

    protected function buildUrl(array $params = []): string
    {
        return parent::buildUrl([':taskId' => $this->productTicketId, ':fileName' => $this->productFileKey]);
    }

    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $result = [
            'product_file_key' => $this->productFileKey,
            'fileId' => $this->fileId,
            'fileContent' => null,
        ];

        try {
            $this->handleStatusCode($response);
            $result['fileContent'] = $response->getContent(false);
            $result['sync_status'] = UserTicketFile::SYNC_SUCCESS;
        } catch (\Throwable $e) {
            $this->log->error("Failed to download file with product hash {$result['product_file_key']}: " . $e->getMessage());
            $result['sync_status'] = UserTicketFile::SYNC_FAILED;
        }

        return $result;
    }
}
