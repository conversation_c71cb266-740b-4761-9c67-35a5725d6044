<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\entities\TaskQueue;

return [ /** [QueueName => [Array Of Assigned Resources]] */
    // Products

    // SMEN
    'GMS' =>    [Res::GMS],
    'CV' =>     [Res::CV, Res::CV_BETTING, Res::SMEN_SPACE_09],
    'GMSD' =>   [Res::GMSD],
    'RUBIN' =>  [Res::RUBIN],
    'ARM' =>    [Res::ARM],
    'PHB' =>    [Res::PHB],
    'VIP' =>    [Res::VIP],
    'EL' =>     [Res::EL],
    'V24' =>    [Res::V24],
    'JC' =>     [Res::JC],
    'ONX' =>     [Res::ONX],
    'RC' =>     [Res::RC],
    'VS' =>     [Res::VS],
    'V777' =>   [Res::V777, Res::V777_BETTING],
    'MS' =>    [Res::MS],
    'SZL' =>    [Res::SZL, Res::SZL_BETTING],
    'S7' =>     [Res::S7, Res::S7_BETTING],
    'LOS' =>    [Res::LOS],
    'VTR' =>    [Res::VTR],
    'AWI' =>    [Res::AWI, Res::AWI_BETTING],
    'K7' =>     [Res::K7, Res::K7_BETTING],
    'VP' =>     [Res::VP, Res::VP_BETTING],
    'MSL' =>    [Res::MSL, Res::MSL_BETTING],
    'VOX' =>    [Res::VOX],
    'WIN' =>    [Res::WIN, Res::WIN_BETTING],

    // GI
    'VV' =>     [Res::VV, Res::VV_BETTING],
    'GGB' =>    [Res::GGB, Res::GGB_BETTING],
    'ICG' =>    [Res::ICG],
    'VERDE' =>  [Res::VERDE, Res::VERDE_BETTING],
    'CSBET' =>  [Res::CSBET],
    'VBET' =>   [Res::VBET, Res::VBET_BETTING],
    'DDBET' =>  [Res::DDBET, Res::DDBET_BETTING],
    'FSC' =>    [Res::FSC, Res::FSC_BETTING],
    'HOT' =>    [Res::HOT],
    'GGUA' =>   [Res::GGUA, Res::GGUA_BETTING],
    'HIT' =>    [Res::HIT, Res::HIT_BETTING],
    'SLTR' =>   [Res::SLTR],
    'BTR' =>    [Res::BTR, Res::BTR_BETTING],
    'VSP' =>    [Res::VSP, Res::VSP_BETTING],
    'NVC' =>    [Res::NVC, Res::NVC_BETTING],
    'FC' =>     [Res::FC, Res::FC_BETTING],

    // YS
    'MRB'   =>  [Res::MRB, Res::MRB_BETTING],
    'SPC'   =>  [Res::SPC],
    'SND'   =>  [Res::SND],
    'GGBUK' =>  [Res::GGBUK],
    'BB'    =>  [Res::BB],
    'KRV'   =>  [Res::KRV],
    'XON'   =>  [Res::XON],

    // Golden Gate
    'GGATE' => [Res::GGD1, Res::GGHD, Res::GGP1],

    // Other resources
    'HHS' =>    Res::PLATFORM_RESOURCES_HHS,
    'TM' =>     [Res::YHLP, Res::L4P, Res::P4E, Res::CRM],
    'OTHER' =>  [Res::COIN_API, Res::OXR, Res::WP, Res::MR, Res::MRT],
    'S2P' =>    Res::PLATFORM_RESOURCES_S2P,
    'BUF' =>    [Res::BUF],
    'SEND' =>   [Res::SEND],
    'CHECK' =>  [Res::CHECK],
    'CHECK_CRITICAL' =>  [Res::CHECK_CRITICAL],
    'DEFAULT' => [Res::DEFAULT],
    'FACES' => [Res::FACES],
    'CID' =>    [Res::CID],
    'WEBMONEY' => [Res::WEBMONEY],
    'BETTING' => [Res::BETTING],
    'STP' =>    [Res::STP, Res::STP_OLD],
    'STP_ADJUST' => [Res::STP_ADJUST],
    'SYNEF_CLOUD' => [Res::SYNEF_CLOUD],
    TaskQueue::QUEUE_ADS => [Res::ADS_UNITY, Res::ADS_IRON, Res::ADS_VUNGLE, Res::ADS_GOOGLE, Res::ADS_FACEBOOK, Res::ADS_MINTEGRAL],
    'VIPAFF' => [Res::VIPAFF, Res::VIPAFF_CP],
    'ROK' => [Res::ROK],
    'YS_ML_SEG' => [Res::YS_ML_SEG],
    'GIN_ML' => [Res::GIN_ML],
    'JIRA' => [Res::JIRA],

    // Queues without default resources
    'SMEN_WD' => [],
    'GI_WD' => [],
    'GI_GAMES' => [],
    'VV_GAMES' => [],

    'SLOW' => [],

    TaskQueue::QUEUE_MANUAL => [], // Queue for manually requested reloads
];
