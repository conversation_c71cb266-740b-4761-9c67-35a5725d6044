<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\DateTimeImmutableWithMicroseconds;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IpValidator;
use app\back\components\validators\StringValidator;

class UserEventCheckout extends BaseEntity
{
    public const int MAX_CONTEXT_ID_LENGTH = 32;

    public const int EVENT_TYPE_INIT = 1;
    public const int EVENT_TYPE_URL_RECEIVED = 2;
    public const int EVENT_TYPE_URL_STARTED = 3;
    public const int EVENT_TYPE_OPENED = 4;
    public const int EVENT_TYPE_SELECTED = 5;
    public const int EVENT_TYPE_TRY_PAY = 6;
    public const int EVENT_TYPE_IFRAME_LOADED = 7;
    public const int EVENT_TYPE_URL_REJECTED = 8;
    public const int EVENT_TYPE_APPBRIDGE_URL_OPEN = 9;
    public const int EVENT_TYPE_APPBRIDGE_URL_TIMEOUT = 10;

    public const array EVENT_TYPES_S2P = [
        self::EVENT_TYPE_OPENED => 'Opened',
        self::EVENT_TYPE_SELECTED => 'Selected',
        self::EVENT_TYPE_TRY_PAY => 'Try to pay',
    ];

    public const array EVENT_TYPES_PRODUCT = [
        self::EVENT_TYPE_INIT => 'Init',
        self::EVENT_TYPE_URL_STARTED => 'Url started',
        self::EVENT_TYPE_URL_RECEIVED => 'Url received',
        self::EVENT_TYPE_URL_REJECTED => 'Url rejected',
        self::EVENT_TYPE_IFRAME_LOADED => 'Iframe loaded',
        self::EVENT_TYPE_APPBRIDGE_URL_OPEN => 'Appbridge url open',
        self::EVENT_TYPE_APPBRIDGE_URL_TIMEOUT => 'Appbridge url timeout',
    ];

    //concat with preserved indexes
    public const array EVENT_TYPES_ALL = self::EVENT_TYPES_S2P + self::EVENT_TYPES_PRODUCT;

    public const int SOURCE_S2P = 1;
    public const int SOURCE_PRODUCT = 2;

    public const array SOURCES = [
        self::SOURCE_S2P => 'S2p',
        self::SOURCE_PRODUCT => 'Product',
    ];

    public const int INSTANCE_S2P = 1;
    public const int INSTANCE_IDEA = 2;
    public const int INSTANCE_W4P = 3;

    public const array INSTANCES = [
        self::INSTANCE_S2P => 'S2P',
        self::INSTANCE_IDEA => 'Idea',
        self::INSTANCE_W4P => 'W4P',
    ];

    #[StringValidator(1, 32)]
    public string $event_id;
    #[IdValidator]
    public ?int $site_id;
    #[BigIdValidator]
    public ?int $user_id;
    #[IntInArrayValidator(self::EVENT_TYPES_ALL)]
    public int $event_type;
    #[StringValidator(1, self::MAX_CONTEXT_ID_LENGTH)]
    public ?string $context_id;
    #[DateTimeImmutableValidator(true)]
    public DateTimeImmutableWithMicroseconds $client_created_at;
    #[DateTimeImmutableValidator(true)]
    public DateTimeImmutableWithMicroseconds $server_received_at;
    #[DateTimeImmutableValidator(true)]
    public ?DateTimeImmutableWithMicroseconds $inserted_at;
    #[IdValidator]
    public ?int $pay_sys_id;
    #[StringValidator(1, 36)]
    public ?string $product_open_id;
    #[IpValidator]
    public ?string $ip;
    #[IdValidator]
    public ?int $useragent_id;
    #[IntInArrayValidator(self::INSTANCES)]
    public ?int $instance_id;
}
