<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntervalValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class UserStatusVipThreshold extends BaseEntity
{
    public const int SITE_ID_ANY = -1;

    public const int CHECK_PERIOD_DAY = 1;
    public const int CHECK_PERIOD_WEEK = 2;
    public const int CHECK_PERIOD_MONTH = 3;

    public const array CHECK_PERIODS = [
        self::CHECK_PERIOD_DAY => 'Day',
        self::CHECK_PERIOD_WEEK => 'Week',
        self::CHECK_PERIOD_MONTH => 'Month',
    ];

    public const array PERIODS = [
        'P1D', 'P7D', 'P30D', 'P1M', 'P90D', 'P3M',
    ];

    public const array STATUSES_UPGRADE_PROCESS_ORDER = [
        User::STATUS_VIP => [
            User::ACTIVE_STATUS_WEAK => [
                [User::STATUS_NORMAL],
            ],
            User::ACTIVE_STATUS_ACTIVE => [
                [User::STATUS_VIP, User::ACTIVE_STATUS_WEAK],
                [User::STATUS_PRE_NORMAL],
                [User::STATUS_NORMAL],
                [User::STATUS_NEW_VIP],
                [User::STATUS_PRE_VIP],
            ],
            User::ACTIVE_STATUS_ACT_IN,
        ],
        User::STATUS_ASP => [
            User::ACTIVE_STATUS_ACTIVE => [
                [User::STATUS_VIP],
            ],
        ],
        User::STATUS_ULTRA => [
            User::ACTIVE_STATUS_ACTIVE => [
                [User::STATUS_ASP],
            ],
        ],
    ];

    public const array STATUSES_DOWNGRADE_PROCESS_ORDER = [
        User::STATUS_VIP => [
            User::ACTIVE_STATUS_ACTIVE => [
                [User::STATUS_ASP],
            ],
            User::ACTIVE_STATUS_WEAK => [
                [User::STATUS_VIP, User::ACTIVE_STATUS_ACTIVE],
            ]
        ],
        User::STATUS_NORMAL => [
            User::ACTIVE_STATUS_HIGH => [
                [User::STATUS_VIP, User::ACTIVE_STATUS_WEAK],
            ],
        ],
    ];

    #[IdValidator]
    public int $id;
    #[StringValidator(2, 2)]
    public string $country;
    #[IntInArrayValidator(User::STATUSES)]
    public int $status;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public int $active_status;
    #[MoneyValidator]
    public string $amount;
    #[IdValidator]
    public ?int $updated_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntValidator(-1)]
    public int $site_id;
    #[BooleanValidator]
    public bool $is_active;
    #[BooleanValidator]
    public bool $is_up;
    #[IntervalValidator(false)]
    public \DateInterval $period;
    #[IntInArrayValidator(self::CHECK_PERIODS)]
    public int $check_period;
}
