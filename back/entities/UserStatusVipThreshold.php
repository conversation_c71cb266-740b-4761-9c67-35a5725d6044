<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntervalValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\JsonArrayOfObjectsValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;

class UserStatusVipThreshold extends BaseEntity
{
    public const int SITE_ID_ANY = -1;

    public const int CHECK_PERIOD_DAY = 1;
    public const int CHECK_PERIOD_WEEK = 2;
    public const int CHECK_PERIOD_MONTH = 3;

    public const array CHECK_PERIODS = [
        self::CHECK_PERIOD_DAY => 'Day',
        self::CHECK_PERIOD_WEEK => 'Week',
        self::CHECK_PERIOD_MONTH => 'Month',
    ];

    public const array PERIODS = [
        'P1D', 'P1W', 'P30D', 'P1M', 'P90D', 'P3M',
    ];

    #[IdValidator]
    public int $id;
    #[StringValidator(2, 2)]
    public string $country;
    #[IntInArrayValidator(User::VIP_STATUSES, true)]
    public int $status;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public int $active_status;
    #[MoneyValidator]
    public string $amount;
    #[IntValidator(1)]
    public ?int $down_multiplier;
    #[IdValidator]
    public ?int $updated_by;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntValidator(-1)]
    public int $site_id;
    #[BooleanValidator]
    public bool $is_up;
    #[BooleanValidator]
    public bool $is_down;
    #[IntervalValidator(false)]
    public \DateInterval $period_up;
    #[IntervalValidator(false)]
    public \DateInterval $period_down;
    #[IntInArrayValidator(self::CHECK_PERIODS)]
    public int $check_period;
    #[JsonArrayOfObjectsValidator]
    public ?array $from_statuses;
}
