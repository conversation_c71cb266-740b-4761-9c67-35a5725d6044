<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserTicketFile;

class UserTicketFiles extends BaseRepository
{
    public const string ENTITY_CLASS = UserTicketFile::class;
    public const string TABLE_NAME = 'users_tickets_files';
    public const array PRIMARY_KEY = ['id'];

    public function getStoragePath(int $fileId, string $extension): string
    {
        $fileName = md5((string) $fileId);
        $folder = substr($fileName, 0, 2);
        return "tickets/$folder/$fileName.$extension";
    }
}
