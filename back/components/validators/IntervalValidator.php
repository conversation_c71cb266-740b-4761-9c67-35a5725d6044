<?php

declare(strict_types=1);

namespace app\back\components\validators;

use app\back\components\helpers\DateHelper;

#[\Attribute]
class IntervalValidator extends BaseValidator
{
    use EmptyStringAndTrimPrepareTrait;

    private readonly bool $emptyStringToNull;
    private readonly bool $trim;

    public function __construct(
        protected readonly bool $asString = true,
        protected readonly ?string $min = null,
        protected readonly ?string $max = null,
    ) {
        $this->emptyStringToNull = true;
        $this->trim = true;
    }

    public function validate(mixed $value, $form, array $context): ?string
    {
        if ($value instanceof \DateInterval) {
            $value = DateHelper::intervalToIso($value);
        }

        if (!is_scalar($value)) {
            return 'is not a scalar';
        }

        if (!preg_match(DateHelper::INTERVAL_PATTERN, (string) $value)) {
            return $this->invalidMessage();
        }

        if (
            $this->min !== null &&
            DateHelper::intervalToSeconds($value) < DateHelper::intervalToSeconds($this->min)
        ) {
            return "less than {$this->min}";
        }

        if (
            $this->max !== null &&
            DateHelper::intervalToSeconds($value) > DateHelper::intervalToSeconds($this->max)
        ) {
            return "more than {$this->max}";
        }

        return null;
    }

    protected function invalidMessage(): string
    {
        return 'is not interval of dates';
    }

    public function cast(mixed $value, $form): null|string|\DateInterval
    {
        if ($value instanceof \DateInterval) {
            if ($this->asString) {
                return DateHelper::intervalToIso($value);
            }

            return $value;
        }

        return $this->asString ? $value : new \DateInterval($value);
    }
}
