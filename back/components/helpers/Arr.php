<?php

declare(strict_types=1);

namespace app\back\components\helpers;

class Arr
{
    public static function fromIterable(iterable $iterable): array
    {
        if ($iterable instanceof \Traversable) {
            return iterator_to_array($iterable);
        }
        return (array)$iterable;
    }

    /** Return true if all $keys present in $arrayToCheck */
    public static function allKeysExists(array $arrayToCheck, array $keys): bool
    {
        return empty(array_diff_key(array_flip($keys), $arrayToCheck));
    }

    /** Return true if any value from $keys present in $arrayToCheck */
    public static function anyKeyExists(array $arrayToCheck, array $keys): bool
    {
        return !empty(array_intersect_key($arrayToCheck, array_flip($keys)));
    }

    /** Return array with only specified keys */
    public static function leaveOnlyKeys(array $arrayToFilter, array $keys, bool $checkAllKeysExists = false): array
    {
        $filtered = array_intersect_key($arrayToFilter, array_flip($keys));

        if ($checkAllKeysExists && count($filtered) !== count($keys)) {
            throw new \InvalidArgumentException('Not found array keys: ' . implode(', ', array_diff($keys, array_keys($filtered))));
        }

        return $filtered;
    }

    public static function removeKeys(array $arrayToFilter, array $keys): array
    {
        return array_diff_key($arrayToFilter, array_flip($keys));
    }

    /** Converts 2-dimensional array to 1-dimensional, saving keys from inner arrays */
    public static function groupsToFlat(array $groups): array
    {
        $result = [];

        foreach ($groups as $items) {
            foreach ($items as $k => $v) {
                $result[$k] = $v;
            }
        }

        return $result;
    }

    /**
     * Converts id => name array to ['id' => id, 'name' => name] array
     * Useful for Vue.js form-grid components
     */
    public static function assocToIdName(array $assocArray, bool $addNull = false, array $additionalKeys = []): array
    {
        $result = [];

        if ($addNull) {
            $result[] = [
                'id' => null,
                'name' => '(empty)',
            ];
        }

        foreach ($assocArray as $id => $name) {
            $element = compact('id', 'name');

            foreach ($additionalKeys as $k => $vals) {
                if (array_key_exists($id, $vals)) {
                    $element[$k] = $vals[$id];
                }
            }

            $result[] = $element;
        }

        return $result;
    }

    /**
     * Sorts array by array of keys in second param
     * All unmatched keys will be at the end of result array with preserved order
     */
    public static function sortByKeys(array $arrayToSort, array $keysOrder): array
    {
        $keysOrderValues = array_flip(array_values($keysOrder));
        $result = $arrayToSort;
        $maxOrder = \count($keysOrderValues);

        uksort($result, static function ($key1, $key2) use ($keysOrderValues, $maxOrder) {
            return ($keysOrderValues[$key1] ?? $maxOrder) <=> ($keysOrderValues[$key2] ?? $maxOrder);
        });

        return $result;
    }

    /**
     * Converts associative array like:
     * ```
     * [
     *      'group' => [
     *          'itemId' => 'itemName',
     *      ],
     * ]
     * ```
     *
     * into array with groups and lists like:
     * ```
     * [
     *      'items' => [
     *          [
     *              'id' => 'itemId'
     *              'name' => 'itemName'
     *          ],
     *          ....
     *      ],
     *      'groups' => [
     *          ['name' => 'group'],
     *      ],
     * ]
     * ```
     *
     * Uses for converting associated arrays and nested array for dropdown, form-grid etc. components
     *  which accepts ['id' => ..., 'name' => ..., 'group' => ...] structure
     */
    public static function assocGroupsToIdName(iterable $groupedItems, string $itemsKey = 'items'): array
    {
        $groups = [];
        $items = [];

        foreach ($groupedItems as $key => $groupItems) {
            if (is_array($groupItems)) {
                $groups[] = ['name' => $key, 'style' => 'light'];

                foreach ($groupItems as $id => $name) {
                    $items[] = ['id' => $id, 'name' => $name, 'group' => $key];
                }
            } else {
                $items[] = ['id' => $key, 'name' => $groupItems];
            }
        }

        return ['groups' => $groups, $itemsKey => $items];
    }

    /**
     * Converts array of values to ['id' => value, 'name' => value] array
     * Useful for Vue.js form-grid components
     */
    public static function columnToIdName(array $column, array $additionalKeys = []): array
    {
        return static::assocToIdName(array_combine($column, $column), false, $additionalKeys);
    }

    public static function groupBy(iterable $rows, array $groups = [], ?string $index = null, ?string $value = null): array
    {
        $result = [];

        $rowValue = static function ($row, $key) {
            return is_object($row) ? $row->$key : $row[$key];
        };

        if (empty($groups)) {
            foreach ($rows as $row) {
                $val = $value === null ? $row : $rowValue($row, $value);

                if ($index !== null) {
                    $result[$rowValue($row, $index)] = $val;
                } else {
                    $result[] = $val;
                }
            }
        } else {
            $group = array_shift($groups);

            foreach ($rows as $row) {
                $result[$rowValue($row, $group)][] = $row;
            }

            foreach ($result as $i => $subRows) {
                $result[$i] = static::groupBy($subRows, $groups, $index, $value);
            }
        }

        return $result;
    }

    public static function assocToKeyValueString(array $arr, string $entireDelimiter = ', ', $kvDelimiter = ': '): string
    {
        $result = [];

        foreach ($arr as $key => $value) {
            $result[] = "{$key}{$kvDelimiter}{$value}";
        }

        return implode($entireDelimiter, $result);
    }

    public static function flatten($twoDimensionArray): array
    {
        return array_merge(...array_values($twoDimensionArray));
    }

    public static function pivot(array $rows, string $groupColumn, string $metricColumn, array $rowColumns = []): array
    {
        $copyColumnPrefixes = ['', '__style__', '__title__'];
        $result = [];
        $groups = [];
        foreach ($rows as $row) {
            $group = $row[$groupColumn] ?? '';
            $newRow = self::leaveOnlyKeys($row, $rowColumns);
            $rowKey = implode('||', $newRow);
            if (!array_key_exists($rowKey, $result)) {
                $result[$rowKey] = $newRow;
            }
            $groups[$group] = true;

            foreach ($copyColumnPrefixes as $prefix) {
                if (array_key_exists($prefix . $metricColumn, $row)) {
                    $result[$rowKey][$prefix . $group] = $row[$prefix . $metricColumn];
                }
            }
        }

        return ['data' => array_values($result), 'groups' => array_keys($groups)];
    }

    public static function uniqueKeys(iterable $rows): array
    {
        $columns = [];
        foreach ($rows as $row) {
            $newColumns = array_diff_key($row, $columns);
            if (!empty($newColumns)) {
                foreach (array_keys($newColumns) as $c) {
                    $columns[$c] = true;
                }
            }
        }

        return array_keys($columns);
    }

    public static function configure(object $object, array $properties, bool $skipNotExistingProperties = false): void
    {
        foreach ($properties as $name => $value) {
            if ($skipNotExistingProperties && !property_exists($object, $name)) {
                continue;
            }
            $object->$name = $value;
        }
    }

    /**
     * Merges two or more arrays into one recursively.
     * If each array has an element with the same string key value, the latter
     * will overwrite the former (different from array_merge_recursive).
     * Recursive merging will be conducted if both arrays have an element of array
     * type and are having the same key.
     * For integer-keyed elements, the elements from the latter array will
     * be appended to the former array.
     */
    public static function merge(...$args): array
    {
        $res = array_shift($args);
        while (!empty($args)) {
            foreach (array_shift($args) as $k => $v) {
                if (is_int($k)) {
                    if (array_key_exists($k, $res)) {
                        $res[] = $v;
                    } else {
                        $res[$k] = $v;
                    }
                } elseif (is_array($v) && isset($res[$k]) && is_array($res[$k])) {
                    $res[$k] = static::merge($res[$k], $v);
                } else {
                    $res[$k] = $v;
                }
            }
        }

        return $res;
    }

    /**
     * Retrieves the value of an array element or object property with the given key or property name.
     * If the key does not exist in the array, the default value will be returned instead.
     * Not used when getting value from an object.
     *
     * The key may be specified in a dot format to retrieve the value of a sub-array or the property
     * of an embedded object. In particular, if the key is `x.y.z`, then the returned value would
     * be `$array['x']['y']['z']`. If `$array['x']` not an array, the default value will be returned.
     * Note that if the array already has an element `x.y.z`, then its value will be returned
     * instead of going through the sub-arrays.
     */
    public static function getValueByPath(array | object $array, string | int $key, mixed $default = null): mixed
    {
        if (is_object($array)) {
            return $array->$key;
        }

        if (array_key_exists($key, $array)) {
            return $array[$key];
        }

        if (($pos = strrpos((string)$key, '.')) !== false) {
            $array = static::getValueByPath($array, substr($key, 0, $pos), $default);

            if (!is_array($array)) {
                return $default;
            }

            $key = substr($key, $pos + 1);

            if (array_key_exists($key, $array)) {
                return $array[$key];
            }
        }

        return $default;
    }

    /**
     * Writes a value into an associative array at the key path specified.
     * If there is no such key path yet, it will be created recursively.
     * If the key exists, it will be overwritten.
     *
     * ```php
     *  $array = [
     *      'key' => [
     *          'in' => [
     *              'val1',
     *              'key' => 'val'
     *          ]
     *      ]
     *  ];
     * ```
     *
     * The result of `Arr::setValue($array, 'key.in.0', ['arr' => 'val']);` will be the following:
     *
     * ```php
     *  [
     *      'key' => [
     *          'in' => [
     *              ['arr' => 'val'],
     *              'key' => 'val'
     *          ]
     *      ]
     *  ]
     *
     * ```
     *
     * The result of
     * `Arr::setValue($array, 'key.in', ['arr' => 'val']);`
     * will be the following:
     *
     * ```php
     *  [
     *      'key' => [
     *          'in' => [
     *              'arr' => 'val'
     *          ]
     *      ]
     *  ]
     * ```
     *
     * the path must be described by a string when each key should be separated by a dot
     * if the path is null then `$array` will be assigned the `$value`
     */
    public static function setValueByPath(array &$array, ?string $path, mixed $value): void
    {
        if ($path === null) {
            $array = $value;
            return;
        }

        $keys = explode('.', $path);

        while (count($keys) > 1) {
            $key = array_shift($keys);
            if (!isset($array[$key])) {
                $array[$key] = [];
            }
            if (!is_array($array[$key])) {
                $array[$key] = [$array[$key]];
            }
            $array = &$array[$key];
        }

        $array[array_shift($keys)] = $value;
    }

    /**
     * Indexes the array according to a specified key.
     * The input should be multidimensional array.
     *
     * The $key should be an anonymous function that must return the value that will be used as a new key.
     *
     * For example:
     *
     * ```php
     * $array = [
     *     ['id' => '123', 'data' => 'abc', 'device' => 'laptop'],
     *     ['id' => '345', 'data' => 'def', 'device' => 'tablet'],
     *     ['id' => '345', 'data' => 'hgi', 'device' => 'smartphone'],
     * ];
     * $result = Arr::index($array, function ($element) {
     *     return $element['id'];
     * });
     * ```
     *
     * The result will be an associative array, where the key is the value of `id` attribute
     *
     * ```php
     * [
     *     '123' => ['id' => '123', 'data' => 'abc', 'device' => 'laptop'],
     *     '345' => ['id' => '345', 'data' => 'hgi', 'device' => 'smartphone']
     *     // The second element of an original array is overwritten by the last element because of the same id
     * ]
     * ```
     */
    public static function index(iterable $array, \Closure|string $key): array
    {
        $result = [];
        foreach ($array as $row) {
            $newKey = is_callable($key) ? $key($row) : $row[$key];
            if ($newKey !== null) {
                $result[$newKey] = $row;
            }
        }

        return $result;
    }

    public static function getColumnAssoc(array $array, string $name): array
    {
        $result = [];
        foreach ($array as $k => $element) {
            $result[$k] = $element[$name] ?? null;
        }

        return $result;
    }

    /**
     * Builds a map (key-value pairs) from a multidimensional array or an array of objects.
     * The `$keyName` and `$valueName` parameters specify the key names or property names to set up the map.
     *
     * For example,
     *
     * ```php
     * $array = [
     *     ['id' => '123', 'name' => 'aaa', 'class' => 'x'],
     *     ['id' => '124', 'name' => 'bbb', 'class' => 'x'],
     *     ['id' => '345', 'name' => 'ccc', 'class' => 'y'],
     * ];
     *
     * $result = Arr::map($array, 'id', 'name');
     * // the result is:
     * // [
     * //     '123' => 'aaa',
     * //     '124' => 'bbb',
     * //     '345' => 'ccc',
     * // ]
     * ```
     */
    public static function map(array $array, string $keyName, string $valueName): array
    {
        $result = [];
        foreach ($array as $element) {
            $key = $element[$keyName];
            $value = $element[$valueName] ?? null;
            $result[$key] = $value;
        }

        return $result;
    }

    public static function batchIterable(iterable $iterable, callable|int $batchSize = 1000): \Generator
    {
        if (is_int($batchSize)) {
            $realBatchSize = $batchSize;
            $batchSize = static function () use ($realBatchSize) {
                static $b = $realBatchSize;
                if (--$b === 0) {
                    $b = $realBatchSize;
                    return true;
                }

                return false;
            };
        }

        $batch = [];
        foreach ($iterable as $v) {
            $batch[] = $v;
            if ($batchSize($v) === true) {
                yield $batch;
                $batch = [];
            }
        }

        if (!empty($batch)) {
            yield $batch;
        }
    }

    public static function sliceIterable(iterable $data, int $limit): array
    {
        return self::batchIterable($data, $limit)->current() ?: [];
    }

    public static function removeDuplicatesByColumns(array &$rows, array $columns, ?string $comparisonKey = null, int $comparisonDir = SORT_DESC): void
    {
        $result = [];
        foreach ($rows as $row) {
            $key = self::getKeyByColumns($columns, $row);

            if ($comparisonKey !== null && array_key_exists($key, $result)) {
                $prev = $result[$key];

                $isNewValue = match ($comparisonDir) {
                    SORT_ASC => $prev[$comparisonKey] >= $row[$comparisonKey],
                    SORT_DESC => $prev[$comparisonKey] <= $row[$comparisonKey]
                };

                if (!$isNewValue) {
                    continue;
                }
            }

            $result[$key] = $row;
        }
        $rows = array_values($result);
    }

    public static function sumDuplicatesByColumns(array &$rows, array $groupColumns, array $sumColumns): void
    {
        $result = [];
        foreach ($rows as $row) {
            $key = self::getKeyByColumns($groupColumns, $row);

            if (array_key_exists($key, $result)) {
                foreach ($sumColumns as $sumColumn) {
                    if (array_key_exists($sumColumn, $result[$key])) {
                        $row[$sumColumn] += $result[$key][$sumColumn];
                    }
                }
            }

            $result[$key] = $row;
        }
        $rows = array_values($result);
    }

    private static function getKeyByColumns(array $columns, mixed $row): string
    {
        $key = [];
        foreach ($columns as $indexOrColumn => $columnOrClosure) {
            if ($columnOrClosure instanceof \Closure) {
                $key[] = $columnOrClosure($row[$indexOrColumn]);
            } else {
                $key[] = $row[$columnOrClosure] ?? '';
            }
        }
        return implode('|', $key);
    }

    public static function removeBlank(array $array): array
    {
        return array_filter($array, static fn($value) => $value !== null && $value !== '');
    }

    public static function median(array $array): ?string
    {
        $countElements = count($array);

        if ($countElements) {
            sort($array);
            $middle = floor($countElements / 2);
            return (string) ($countElements % 2 === 1 ? $array[$middle] : ($array[$middle - 1] + $array[$middle]) / 2);
        }
        return null;
    }

    public static function flipWithMultipleKeys(array $arr): array
    {
        return array_reduce(array_keys($arr), static function ($values, $key) use (&$arr) {
            $values[$arr[$key]] ??= [];
            $values[$arr[$key]][] = $key;
            return $values;
        }, []);
    }

    public static function arraySumRecursive(array $array): int
    {
        $sum = 0;
        foreach ($array as $child) {
            if (is_numeric($child) || $child === null) {
                $sum += $child;
            } elseif (is_array($child)) {
                $sum += self::arraySumRecursive($child);
            } else {
                throw new \RuntimeException("Can't sum variable of type " . gettype($child));
            }
        }
        return $sum;
    }
}
