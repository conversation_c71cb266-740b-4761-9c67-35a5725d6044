<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250521_122523_users_tickets_files_add_product_key_column extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()->addColumn('users_tickets_files', 'product_file_key', 'varchar(32)')->execute();
        $this->sql('CREATE UNIQUE INDEX ON users_tickets_files (product_file_key, ticket_id)');

        $this->db->createCommand()->alterColumn('users_tickets_log', 'created_by', 'DROP NOT NULL')->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->dropColumn('users_tickets_files', 'product_file_key')->execute();
        $this->db->createCommand()->alterColumn('users_tickets_log', 'created_by', 'SET NOT NULL')->execute();
    }
}
