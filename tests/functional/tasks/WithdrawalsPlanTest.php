<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\config\tasks\Res;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\entities\Withdrawal;
use app\back\modules\task\actions\update\WithdrawalsPlannedTask;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use app\back\repositories\WithdrawalRestrictions;
use app\back\repositories\Withdrawals;
use app\tests\libs\mock\MockServer;
use app\tests\libs\DbTransactionalUnitTrait;
use Nyholm\Psr7\Request;
use PHPUnit\Framework\Attributes\After;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(WithdrawalsPlannedTask::class)]
class WithdrawalsPlanTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private array $withdrawalToStatusMap = [];

    public function testWithdrawalFromPlannedToNewToSync(): void
    {
        $this->haveSystemUser();
        $this->haveRates();

        $withdrawalNew = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_NEW,
            'transaction_id' => '110000000000000',
        ]);
        $withdrawalPlannedNoWallets = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED,
            'transaction_id' => '110000000000001',
        ]);
        $withdrawalPlannedAbort = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED_ABORT,
        ]);
        $withdrawalPlannedMoneyEnough = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED,
            'transaction_id' => '110000000000003',
        ], $statMoneyEnough);
        $withdrawalPlannedMoneyNotEnough = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED,
            'transaction_id' => $withdrawalPlannedMoneyEnough->user_id,
        ], $statMoneyNotEnough);
        $withdrawalPlannedInFuture = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED,
            'planned_at' => new \DateTimeImmutable('+1 day'),
        ]);
        $withdrawalPlannedSelfSyncError = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_SYNC_ERROR,
            'transaction_id' => '110000000000006',
        ], $statSyncError);
        $withdrawalPlannedHavingSyncError = $this->haveWithdrawal([
            'status' => Withdrawal::STATUS_PLANNED,
        ], $statSyncError);

        $this->haveWallet($statMoneyEnough, ['balance' => $statMoneyEnough->amount_orig]);
        $this->haveWallet($statMoneyNotEnough, ['balance' => $statMoneyNotEnough->amount_orig - .01]);

        MockServer::with(fn() => $this->runTask('sync-withdrawals', Res::GGB), [
            new Request('POST', "/api/payment/$withdrawalNew->transaction_id/brand/4/accept?signature=2b163e84f3e300d7000d786adec14079"),
        ]);
        $this->seeWithdrawalsInStatus([
            $withdrawalNew->id => Withdrawal::STATUS_SYNCED,
            $withdrawalPlannedNoWallets->id => Withdrawal::STATUS_PLANNED,
            $withdrawalPlannedAbort->id => Withdrawal::STATUS_PLANNED_ABORT,
            $withdrawalPlannedMoneyEnough->id => Withdrawal::STATUS_PLANNED,
            $withdrawalPlannedMoneyNotEnough->id => Withdrawal::STATUS_PLANNED,
            $withdrawalPlannedInFuture->id => Withdrawal::STATUS_PLANNED,
            $withdrawalPlannedHavingSyncError->id => Withdrawal::STATUS_PLANNED,
            $withdrawalPlannedSelfSyncError->id => Withdrawal::STATUS_SYNC_ERROR,
        ]);

        $this->runTask('update-withdrawals-planned', Res::GGB);
        $this->seeWithdrawalsInStatus([
            $withdrawalPlannedNoWallets->id => Withdrawal::STATUS_NEW,
            $withdrawalPlannedMoneyEnough->id => Withdrawal::STATUS_NEW,
            $withdrawalPlannedHavingSyncError->id => Withdrawal::STATUS_NEW,
            $withdrawalPlannedMoneyNotEnough->id => Withdrawal::STATUS_PLANNED_ABORT,
        ]);

        MockServer::with(fn() => $this->runTask('sync-withdrawals', Res::GGB), [
            new Request('POST', "/api/payment/$withdrawalPlannedNoWallets->transaction_id/brand/4/accept?signature=671443f562ce16f3747307cb6cdf23b1"),
            new Request('POST', "/api/payment/$withdrawalPlannedMoneyEnough->transaction_id/brand/4/accept?signature=b5ac10a485ac20a706f4c26f25685855"),
            new Request('POST', "/api/payment/$withdrawalPlannedHavingSyncError->transaction_id/brand/4/accept?signature=160b56f2b69bd6e227fc381c3986556a"),
        ]);

        $this->seeWithdrawalsInStatus([
            $withdrawalPlannedNoWallets->id => Withdrawal::STATUS_SYNCED,
            $withdrawalPlannedMoneyEnough->id => Withdrawal::STATUS_SYNCED,
            $withdrawalPlannedHavingSyncError->id => Withdrawal::STATUS_SYNCED,
        ]);
    }

    public function testWithdrawalPlannedRestrictions(): void
    {
        $this->haveSystemUser();
        $this->haveRates();
        $this->db()->createCommand()->insert(WithdrawalRestrictions::TABLE_NAME, [
            'site_id' => Site::GGB,
            'daily_sum' => 100,
            'updated_at' => 'NOW()',
        ])->execute();

        $statToSuccess = $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'currency' => Rate::RUB,
            'amount_orig' => 100,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);
        $withdrawalToSuccess = $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED], $statToSuccess);
        $this->runTask('update-withdrawals-planned', Res::GGB);
        $this->seeWithdrawalsInStatus([$withdrawalToSuccess->id => Withdrawal::STATUS_NEW]);
        $withdrawalToSuccess->status = Withdrawal::STATUS_SYNCED;
        $this->container()->get(Withdrawals::class)->update($withdrawalToSuccess, ['status', 'decision']);
        $statToSuccess->status = UserTransaction::STATUS_SUCCESS;
        $this->container()->get(UserTransactions::class)->update($statToSuccess, ['status']);

        $statToAbort = $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'currency' => Rate::RUB,
            'amount_orig' => 100,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);
        $withdrawalToAbort = $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED], $statToAbort);
        $this->runTask('update-withdrawals-planned', Res::GGB);

        $this->seeWithdrawalsInStatus([
            $withdrawalToSuccess->id => Withdrawal::STATUS_SYNCED,
            $withdrawalToAbort->id => Withdrawal::STATUS_PLANNED_ABORT,
        ]);
        $this->runTask('sync-withdrawals', Res::GGB);
        $this->seeWithdrawalsInStatus([]);
    }

    public function testWithdrawalPlannedAbortedReject(): void
    {
        $this->haveSystemUser();
        $this->haveRates();

        $userStatNoWithdrawalNoWallet = $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);
        $userStatNoWithdrawalMoneyEnough = $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);
        $userStatNoWithdrawalMoneyNotEnough = $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);

        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED], $statNoWallet1);
        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED], $statMoneyEnough1);
        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED], $statMoneyNotEnough1);

        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED_ABORT], $statNoWallet2);
        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED_ABORT], $statMoneyEnough2);
        $this->haveWithdrawal(['status' => Withdrawal::STATUS_PLANNED_ABORT], $statMoneyNotEnough2);

        $this->haveWallet($statMoneyEnough1, ['balance' => $statMoneyEnough1->amount_orig]);
        $this->haveWallet($statMoneyEnough2, ['balance' => $statMoneyEnough2->amount_orig]);
        $this->haveWallet($userStatNoWithdrawalMoneyEnough, ['balance' => $userStatNoWithdrawalMoneyEnough->amount_orig]);
        $this->haveWallet($statMoneyNotEnough1, ['balance' => $statMoneyNotEnough1->amount_orig - 1]);
        $this->haveWallet($statMoneyNotEnough2, ['balance' => $statMoneyNotEnough2->amount_orig - 1]);
        $this->haveWallet($userStatNoWithdrawalMoneyNotEnough, ['balance' => $userStatNoWithdrawalMoneyNotEnough->amount_orig - 1]);

        $this->runTask('update-withdrawals-reject', Res::GGB);

        $denyWithdraw = static fn(UserTransaction $us) => [
            'site_id' => $us->site_id,
            'transaction_id' => $us->transaction_id,
            'user_id' => $us->user_id,
            'status' => Withdrawal::STATUS_NEW,
            'decision' => Withdrawal::DECISION_DENY,
        ];

        $this->seeRecords(Withdrawals::class, array_map($denyWithdraw, [
            $statMoneyNotEnough2,
            $userStatNoWithdrawalMoneyNotEnough,
        ]));
        $this->dontSeeRecords(Withdrawals::class, array_map($denyWithdraw, [
            $userStatNoWithdrawalNoWallet,
            $userStatNoWithdrawalMoneyEnough,
            $statNoWallet1,
            $statMoneyEnough1,
            $statMoneyNotEnough1,
            $statNoWallet2,
            $statMoneyEnough2,
        ]));
    }

    private function seeWithdrawalsInStatus(array $withdrawalToStatusMap): void
    {
        $this->withdrawalToStatusMap = $withdrawalToStatusMap + $this->withdrawalToStatusMap;
        foreach ($this->withdrawalToStatusMap as $id => $status) {
            $this->seeRecordWithFields(Withdrawals::class, ['id' => $id], ['status' => $status]);
        }
    }

    #[After]
    protected function clearWithdrawalToStatusMap(): void
    {
        $this->withdrawalToStatusMap = [];
    }

    private function haveWallet(UserTransaction $stat, array $attributes): void
    {
        $this->haveRecord(UserWallets::class, [
            'site_id' => $stat->site_id,
            'user_id' => $stat->user_id,
            'wallet_id' => self::uniqRuntimeId(),
            'type' => UserWallet::TYPE_REAL,
            'currency' => $stat->currency,
            ...$attributes,
        ]);
    }

    private function haveWithdrawal(array $attributes, ?UserTransaction &$stat = null): Withdrawal
    {
        $stat ??= $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'transaction_id' => $attributes['transaction_id'] ?? $stat?->transaction_id,
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'dir' => UserTransaction::DIR_OUT,
        ]);

        if (in_array($attributes['status'], [Withdrawal::STATUS_PLANNED, Withdrawal::STATUS_PLANNED_ABORT], true)) {
            $attributes['planned_at'] ??= new \DateTimeImmutable('-1 minute');
        }

        $record = $this->haveRecord(Withdrawals::class, [
            'site_id' => $stat->site_id,
            'transaction_id' => $stat->transaction_id,
            'user_id' => $stat->user_id,
            'operator_id' => $this->haveEmployee()->employee_id,
            'decision' => Withdrawal::DECISION_ALLOW,
            'created_at' => new \DateTimeImmutable('-1 minute'),
            ...$attributes,
        ]);

        self::assertInstanceOf(Withdrawal::class, $record);

        return $record;
    }
}
