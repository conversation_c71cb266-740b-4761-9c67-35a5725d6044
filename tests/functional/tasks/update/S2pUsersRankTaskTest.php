<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\components\helpers\DateHelper;
use app\back\entities\S2pOrder;
use app\back\entities\S2pUser;
use app\back\entities\Site;
use app\back\modules\task\actions\update\S2pUsersRankTask;
use app\back\repositories\S2pUsers;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(S2pUsersRankTask::class)]
class S2pUsersRankTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testRouteFreePaidPlayedNewVipVIP(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(1)])->user_id;
        $this->haveRates();

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 250,
            'is_first_success' => true,
            'date' => new \DateTimeImmutable('-6 hours'),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PAID,
        ]);

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId, 'bet_amount' => 10, 'created_at' => new \DateTimeImmutable('-5 hours'),]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 200,
            'date' => new \DateTimeImmutable('-5 hours'),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_NEW_VIP,
        ]);

        $startTimeAfter2Days = date('Y-m-d H:i:s', strtotime('+1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTimeAfter2Days, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_VIP,
        ]);
    }

    public function testRoutePlayedPreVipVIP(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(1)])->user_id;
        $this->haveRates();

        $this->haveRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));

        for ($i = 1; $i <= 11; ++$i) {
            $this->haveS2pOrderRecord([
                'status' => S2pOrder::STATUS_SUCCESS,
                'type' => S2pOrder::TYPE_IN,
                'site_id' => $siteId,
                'user_id' => $userId,
                'date' => new \DateTimeImmutable("-$i hours"),
            ]);
        }

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_OUT,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable("-1 hours"),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PRE_VIP,
        ]);

        $startTimeAfter2Days = date('Y-m-d H:i:s', strtotime('+1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTimeAfter2Days, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_VIP,
        ]);
    }

    public function testRoutePlayedPreNormalNormal(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(1)])->user_id;
        $this->haveRates();

        $this->haveRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);

        for ($i = 1; $i < 3; ++$i) {
            $this->haveS2pOrderRecord([
                'status' => S2pOrder::STATUS_SUCCESS,
                'type' => S2pOrder::TYPE_IN,
                'site_id' => $siteId,
                'user_id' => $userId,
                'summ_eur' => 30,
                'date' => new \DateTimeImmutable("-$i hours"),
            ]);
        }

        $startTimeAfter2Days = date('Y-m-d H:i:s', strtotime('+1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTimeAfter2Days, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 20,
            'date' => new \DateTimeImmutable("-1 hours"),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTimeAfter2Days, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PRE_NORMAL,
        ]);

        for ($i = 1; $i <= 3; ++$i) {
            $this->haveS2pOrderRecord([
                'status' => S2pOrder::STATUS_SUCCESS,
                'type' => S2pOrder::TYPE_IN,
                'site_id' => $siteId,
                'user_id' => $userId,
                'summ_eur' => 5,
                'date' => new \DateTimeImmutable("-$i hours"),
            ]);
        }

        $this->runTask('update-s2p-users-rank', 'S2P', $startTimeAfter2Days, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_NORMAL,
        ]);
    }

    public function testRouteNormalVipSuperVipUltraVip(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(31)])->user_id;
        $this->haveRates();

        $this->haveRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_NORMAL,
        ]);

        for ($i = 1; $i <= 11; ++$i) {
            $this->haveS2pOrderRecord([
                'status' => S2pOrder::STATUS_SUCCESS,
                'type' => S2pOrder::TYPE_IN,
                'site_id' => $siteId,
                'user_id' => $userId,
                'summ_eur' => 5,
                'date' => new \DateTimeImmutable("-$i days"),
            ]);
        }

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_OUT,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable("-1 hour"),
        ]);

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_VIP,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 1450,
            'date' => new \DateTimeImmutable("-1 day"),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_SUPER_VIP,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 4000,
            'date' => new \DateTimeImmutable("-1 day"),
        ]);

        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_ULTRA_VIP,
        ]);
    }


    public function testRoutePreNormalPlayed(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(91)])->user_id;
        $this->haveRates();

        $this->haveRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PRE_NORMAL,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 5,
            'date' => new \DateTimeImmutable("-91 days"),
        ]);

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);
    }

    public function testRouteVipPlayed(): void
    {
        $siteId = Site::CV;

        $userId = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::daysAgo(91)])->user_id;
        $this->haveRates();

        $this->haveRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PRE_VIP,
        ]);

        $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'summ_eur' => 100,
            'date' => new \DateTimeImmutable("-10 days"),
        ]);

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));
        $this->runTask('update-s2p-users-rank', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pUsers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
            'rank' => S2pUser::RANK_PLAYED,
        ]);
    }

}
