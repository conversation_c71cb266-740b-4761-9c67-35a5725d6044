<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\BettingBet;
use app\back\entities\BettingOdd;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\modules\task\actions\update\BettingOddsPartsTask;
use app\back\repositories\BettingBets;
use app\back\repositories\BettingOdds;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(BettingOddsPartsTask::class)]
class BettingOddPartsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    #[DataProvider('dataProviderSingle')]
    #[DataProvider('dataProviderExpress')]
    #[DataProvider('dataProviderSystem')]
    #[DataProvider('dataProviderHandicap')]
    public function testAll(int $type, int $status, array $oddsAndExpected): void
    {
        $this->haveRates();
        $today = new \DateTimeImmutable();

        $bet = $this->createBet($type, $status);
        $oddsExpected = [];
        foreach ($oddsAndExpected as [$ratio, $oddStatus, $stakePart, $refundPart]) {
            $odd = $this->createOdd($bet, $ratio, $oddStatus);
            $oddsExpected[] = [$odd->odd_id, $stakePart, $refundPart];
        }

        $this->runTask('update-betting-odds-parts', Res::VERDE_BETTING, $today->format('Y-m-d'), 'P1D');

        foreach ($oddsExpected as [$oddId, $stakePart, $refundPart]) {
            $this->seeRecordWithFields(BettingOdds::class, ['odd_id' => $oddId], ['stake_part' => $stakePart, 'refund_part' => $refundPart]);
        }
    }

    public static function dataProviderSingle(): array
    {
        return [
            [BettingBet::TYPE_SINGLE, BettingBet::STATUS_ACCEPTED, [
                [1.1, BettingOdd::STATUS_NOT_RESULTED, 1.0, 0.0],
            ]],
            [BettingBet::TYPE_SINGLE, BettingBet::STATUS_ACCEPTED, [ // Odd win, but bet is not settled
                [1.2, BettingOdd::STATUS_WIN, 1.0, 0.0],
            ]],
            [BettingBet::TYPE_SINGLE, BettingBet::STATUS_SETTLED, [ // Odd win, bet settled, common valid case
                [1.3, BettingOdd::STATUS_WIN, 1.0, 1.0],
            ]],
            [BettingBet::TYPE_SINGLE, BettingBet::STATUS_SETTLED, [ // Status of odd is not refundable
                [1.4, BettingOdd::STATUS_LOSS, 1.0, 0.0],
            ]],
        ];
    }

    public static function dataProviderExpress(): array
    {
        return [
            [BettingBet::TYPE_EXPRESS, BettingBet::STATUS_SETTLED, [
                [1.5, BettingOdd::STATUS_NOT_RESULTED, 0.5, 0.0],
                [1.5, BettingOdd::STATUS_NOT_RESULTED, 0.5, 0.0],
            ]],
            [BettingBet::TYPE_EXPRESS, BettingBet::STATUS_SETTLED, [
                [2, BettingOdd::STATUS_WIN, 0.5, 0.5],
                [2, BettingOdd::STATUS_WIN, 0.5, 0.5],
            ]],
            [BettingBet::TYPE_EXPRESS, BettingBet::STATUS_SETTLED, [
                [3.0, BettingOdd::STATUS_WIN, 0.8, 0.8],
                [1.5, BettingOdd::STATUS_WIN, 0.2, 0.2],
            ]],
            [BettingBet::TYPE_EXPRESS, BettingBet::STATUS_SETTLED, [
                [1.2, BettingOdd::STATUS_WIN, 0.125, 0.2],
                [1.2, BettingOdd::STATUS_WIN, 0.125, 0.2],
                [1.6, BettingOdd::STATUS_WIN, 0.375, 0.6],
                [1.6, BettingOdd::STATUS_LOSS, 0.375, 0.0],
            ]],
        ];
    }

    public static function dataProviderSystem(): array
    {
        return [
            [BettingBet::TYPE_SYSTEM, BettingBet::STATUS_SETTLED, [ // Same ratio, as in express
                [3.0, BettingOdd::STATUS_WIN, 0.8, 0.8],
                [1.5, BettingOdd::STATUS_WIN, 0.2, 0.2],
            ]],
            [BettingBet::TYPE_SYSTEM, BettingBet::STATUS_SETTLED, [
                [1.2, BettingOdd::STATUS_WIN, 0.25, 0.25],
                [1.2, BettingOdd::STATUS_LOSS, 0.25, 0.0],
                [1.6, BettingOdd::STATUS_WIN, 0.5, 0.75],
            ]],
            [BettingBet::TYPE_SYSTEM, BettingBet::STATUS_SETTLED, [
                [1.2, BettingOdd::STATUS_WIN, 1 / 6, 0.25],
                [1.2, BettingOdd::STATUS_WIN, 1 / 6, 0.25],
                [1.6, BettingOdd::STATUS_WIN, 2 / 6, 0.5],
                [1.6, BettingOdd::STATUS_LOSS, 2 / 6, 0.0],
            ]],
            [BettingBet::TYPE_SYSTEM, BettingBet::STATUS_SETTLED, [
                [1.5, BettingOdd::STATUS_WIN, 0.2, 1 / 3],
                [1.5, BettingOdd::STATUS_WIN, 0.2, 1 / 3],
                [1.5, BettingOdd::STATUS_WIN, 0.2, 1 / 3],
                [1.5, BettingOdd::STATUS_LOSS, 0.2, 0.0],
                [1.5, BettingOdd::STATUS_LOSS, 0.2, 0.0],
            ]],
        ];
    }

    // Example: https://stat.ltd/reports/betting?f0n=site_id&f0v=,29&f1n=currency&f1v=EUR&f2n=date_type&f2v=created_at&f3n=bet_id&f3v=9FlDnG23TSGeI95Pnaqif2gYowsJPJ0AAKr4LgBJ%0AdypNM6ZLTc6YMzhLUU3B5WgaCuQAqfoAAL0EkQAk%0AfsZtX6%2BjTBSWr0dR7mPoAmgVD/ICnX0AALlnegRg%0AGCU%2B9EgcTm2tfm4Aexi8hGgejHkLWtoAAE1iyQIB%0ApLOyV/HWTBuZ0QMSU9GvG2gXyp0Hf7sAALlnegXc%0AxHLB9sXQSZiasgs8hpgBF2gYSm8I1V8AAKr4LgAf%0Ay0AHtGrWTw2kCkjVat9uQGgjexsOrsgAAE1iyQRh&f4n=date&f4v=2025-04-01&f4o=%3E=&columns=,user_id,bet_id,stake_sum,refund_sum,odd_ratio,odd_status,odd_name,market_name,event_name&metrics=,stake_sum,refund_sum
    public static function dataProviderHandicap(): array
    {
        return [
            [BettingBet::TYPE_SINGLE, BettingBet::STATUS_SETTLED, [ // Asian handicap half win, Example: (768 / 2) * 1.53 + (768 / 2) * 1 = 971.52, final ratio = 1.265
                [1.5, BettingOdd::STATUS_HALF_WIN, 1.0, 1.0],
            ]],
            [BettingBet::TYPE_EXPRESS, BettingBet::STATUS_SETTLED, [
                [1.6, BettingOdd::STATUS_WIN, 0.4, 0.4],
                [1.9, BettingOdd::STATUS_HALF_WIN, 0.6, 0.6],
            ]],
        ];
    }

    private function createBet(int $type, int $status): BettingBet
    {
        $dt = new \DateTimeImmutable();
        $stake = random_int(10, 20);
        $refund = $stake + random_int(1, 5);

        /** @var BettingBet $bet */
        $bet = $this->haveRecord(BettingBets::class, [
            'id' => self::uniqRuntimeId(),
            'site_id' => Site::VERDE,
            'user_id' => self::uniqRuntimeId(),
            'status' => $status,
            'type' => $type,
            'system_sizes' => [2], // Only for system test
            'stake' => $stake,
            'stake_usd' => $stake,
            'stake_rub' => $this->ratesRepo()->convert($stake, Rate::USD, Rate::RUB, $dt->format('Y-m-d H:i:s')),
            'stake_eur' => $this->ratesRepo()->convert($stake, Rate::USD, Rate::EUR, $dt->format('Y-m-d H:i:s')),
            'refund' => $refund,
            'refund_usd' => $refund,
            'refund_rub' => $this->ratesRepo()->convert($refund, Rate::USD, Rate::RUB, $dt->format('Y-m-d H:i:s')),
            'refund_eur' => $this->ratesRepo()->convert($refund, Rate::USD, Rate::EUR, $dt->format('Y-m-d H:i:s')),
            'currency' => Rate::USD,
            'created_at' => $dt->sub(new \DateInterval('PT2H')),
            'updated_at' => $dt->sub(new \DateInterval('PT1H')),
        ]);

        return $bet;
    }

    private function createOdd(BettingBet $bet, float $ratio, int $status): BettingOdd
    {
        /** @var BettingOdd $odd */
        $odd = $this->haveRecord(BettingOdds::class, [
            'odd_id' => self::uniqRuntimeId(),
            'event_id' => self::uniqRuntimeId(),
            'market_id' => self::uniqRuntimeId(),
            'bet_id' => $bet->id,
            'ratio' => $ratio,
            'status' => $status,
        ]);

        return $odd;
    }
}
