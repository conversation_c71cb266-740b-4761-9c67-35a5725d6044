<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\entities\Site;
use app\back\entities\UserMetric;
use app\back\modules\task\actions\update\UsersLoginsRegTask;
use app\back\repositories\UserLogins;
use app\back\repositories\UserMetrics;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersLoginsRegTask::class)]
class UsersLoginsRegTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    public function testFistLogin(): void
    {
        $siteId = Site::CV;
        $this->haveRates();

        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 day')));
        $user = $this->haveUserRecord(['site_id' => $siteId, 'date' => $dateReg]);
        $user2 = $this->haveUserRecord(['site_id' => $siteId, 'date' => $dateReg]);

        $startTime = date('Y-m-d H:i:00', strtotime('-1 minute'));

        $firstSuccessLogin = $this->haveUserLogin($user, $this->timeShift($startTime, '+30 second'), ['success' => true]);
        $secondSuccessLogin = $this->haveUserLogin($user, $this->timeShift($startTime, '+50 second'), ['success' => true]);

        $firstFailLoginUser2 = $this->haveUserLogin($user2, $startTime, ['success' => false]);

        $this->runTask('update-users-logins-reg', 'CV', $startTime, 'P1D');

        $this->seeRecords(UserLogins::class, [
            [
                'login_id' => $firstSuccessLogin->login_id,
                'reg' => true,
            ], [
                'login_id' => $secondSuccessLogin->login_id,
                'reg' => false,
            ], [
                'login_id' => $firstFailLoginUser2->login_id,
                'reg' => false,
            ]
        ]);

        [$metricId, $metricCol] = UserMetric::M_LOGIN_FIRST_AT;

        $this->seeRecord(UserMetrics::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'metric' => $metricId,
            $metricCol => $firstSuccessLogin->date->format('Y-m-d H:i:s')
        ]);

        $this->dontSeeRecords(UserMetrics::class, [
            [
                'site_id' => $user2->site_id,
                'user_id' => $user2->user_id,
                'metric' => $metricId,
            ],
        ]);

        $thirdSuccessOrderWithEarlyDate = $this->haveUserLogin($user, $this->timeShift($startTime, '+5 second'), ['success' => true]);

        $this->runTask('update-users-logins-reg', 'CV', $startTime, 'P1D');

        $this->seeRecords(UserLogins::class, [
            [
                'login_id' => $thirdSuccessOrderWithEarlyDate->login_id,
                'reg' => true,
            ], [
                'login_id' => $firstSuccessLogin->login_id,
                'reg' => false,
            ]
        ]);
        $this->seeRecord(UserMetrics::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'metric' => $metricId,
            $metricCol => $thirdSuccessOrderWithEarlyDate->date->format('Y-m-d H:i:s')
        ]);
    }
}
