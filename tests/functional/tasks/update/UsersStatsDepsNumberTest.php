<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersTransactionsDepsNumberTask;
use app\back\repositories\UserTransactions;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersTransactionsDepsNumberTask::class)]
class UsersStatsDepsNumberTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testDepNums(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $d = '2023-12-22';
        $this->haveRates();

        $statNew = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:44:00"),
            'updated_at' => new \DateTimeImmutable("$d 22:44:10"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_NEW,
        ]);
        $statFail = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:44:20"),
            'updated_at' => new \DateTimeImmutable("$d 22:45:00"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_FAIL,
        ]);
        $statOut = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:44:30"),
            'updated_at' => new \DateTimeImmutable("$d 22:45:30"),
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);
        $statOk = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:46:50"),
            'updated_at' => new \DateTimeImmutable("$d 22:48:21"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);

        $this->runTask('update-users-transactions-dep-number', 'CV', $d, 'P1D');

        foreach ([$statNew, $statFail, $statOut] as $item) {
            $this->seeRecordWithFields(UserTransactions::class, [
                'site_id' => $item->site_id,
                'transaction_id' => $item->transaction_id,
            ], [
                'dep_number' => null,
            ]);
        }

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $statOk->site_id,
            'transaction_id' => $statOk->transaction_id,
        ], [
            'dep_number' => 1,
        ]);
    }

    public function testWithUpdatePrevDep(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $d = '2023-12-22';
        $this->haveRates();

        $userStat1 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:44:00"),
            'updated_at' => new \DateTimeImmutable("$d 22:44:10"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_NEW,
        ]);

        $userStat2 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 22:46:50"),
            'updated_at' => new \DateTimeImmutable("$d 22:48:21"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);

        $this->runTask('update-users-transactions-dep-number', 'CV', $d, 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat1->site_id,
            'transaction_id' => $userStat1->transaction_id,
        ], [
            'dep_number' => null,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat2->site_id,
            'transaction_id' => $userStat2->transaction_id,
        ], [
            'dep_number' => 1,
        ]);

        $userStat1->status = UserTransaction::STATUS_SUCCESS;
        $this->repo(UserTransactions::class)->update($userStat1, ['status']);
        $this->runTask('update-users-transactions-dep-number', 'CV', $d, 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat1->site_id,
            'transaction_id' => $userStat1->transaction_id,
        ], [
            'dep_number' => 1,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat2->site_id,
            'transaction_id' => $userStat2->transaction_id,
        ], [
            'dep_number' => 2,
        ]);
    }
}
