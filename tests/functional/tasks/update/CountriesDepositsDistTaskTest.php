<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\Rate;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\CountriesDepsDistrTask;
use app\back\repositories\CountryDepDistrs;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Group;

#[Group('slow')] // excluded from default test suite, to run all slow tests with cmd ./bin/phpunit --group slow
#[CoversClass(CountriesDepsDistrTask::class)]
class CountriesDepositsDistTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private \DateTimeImmutable $dateReg1;
    private \DateTimeImmutable $dateReg2;
    private int $registerUsers;

    public function setUp(): void
    {
        $this->dateReg1 = new \DateTimeImmutable('-15 day');
        $this->dateReg2 = new \DateTimeImmutable('-16 day');
        $this->registerUsers = CountriesDepsDistrTask::COUNTRY_ELIGIBLE_USERS_COUNT + 200;
    }

    public function testBasic(): void
    {
        $this->haveRates();
        $siteBrazil1 = self::uniqSiteId();
        $siteBrazil2 = self::uniqSiteId();
        $siteAustria = self::uniqSiteId();

        for ($i = 0; $i < $this->registerUsers; $i++) {
            $usersBrazil1[$i] = $this->haveUserRecord(['country' => 'BR', 'site_id' => $siteBrazil1, 'date' => $this->dateReg1]);
            $usersBrazil2[$i] = $this->haveUserRecord(['country' => 'BR', 'site_id' => $siteBrazil2, 'date' => $this->dateReg2]);
            $usersAustria[$i] = $this->haveUserRecord(['country' => 'AT', 'site_id' => $siteAustria, 'date' => $this->dateReg2]);

            $this->recordDeposit($usersBrazil1[$i]->user_id, $siteBrazil1, '100.00', $this->dateReg1);
            $this->recordDeposit($usersBrazil2[$i]->user_id, $siteBrazil2, '100.00', $this->dateReg1);

            $this->recordDeposit($usersAustria[$i]->user_id, $siteAustria, '10.00', $this->dateReg1);
            if ($i % 2  === 0) {
                $this->recordDeposit($usersAustria[$i]->user_id, $siteAustria, '10.00', $this->dateReg1); // 2nd dep for 50% of users
            }
        }

        $this->runTask('update-countries-deps-distr', Res::DEFAULT);
        $this->seeRecordWithFields(CountryDepDistrs::class, ['country' => 'BR', 'percentile' => 99], ['sites_count' => 2, 'users_count' => 1000]);
        $this->seeRecordWithFields(CountryDepDistrs::class, ['country' => 'AT', 'percentile' => 25], ['amount_usd' => '10.00']);
        $this->seeRecordWithFields(CountryDepDistrs::class, ['country' => 'AT', 'percentile' => 50], ['amount_usd' => '15.00']);
        $this->seeRecordWithFields(CountryDepDistrs::class, ['country' => 'AT', 'percentile' => 75], ['amount_usd' => '20.00']);
    }

    private function recordDeposit(int $user_id, int $site_id, string $amountUsd, \DateTimeImmutable $date): void
    {
        $this->haveUserTransactionRecord([
            'user_id' => $user_id,
            'site_id' => $site_id,
            'amount_orig' => $amountUsd,
            'currency' => Rate::USD,
            'status' => UserTransaction::STATUS_SUCCESS,
            'op_id' => UserTransaction::OP_IN,
            'updated_at' => $date
        ]);
    }
}
