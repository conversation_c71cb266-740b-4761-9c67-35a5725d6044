<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\Game;
use app\back\entities\GameVendor;
use app\back\entities\Site;
use app\back\entities\UserGameRaw;
use app\back\repositories\Games;
use app\back\repositories\GameVendors;
use app\back\repositories\UserGameRaws;
use app\back\repositories\UserGameTokens;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;

class UsersGamesAggregateTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string DATA = <<<DATA
id,player_id,type,game_id,game_name,vendor_id,platform,session_token,round_type,currency,real_amount,real_amount_after,bonus_amount,bonus_amount_after,created_at,inserted_at
113,1,bet,slotoplex_pragmatic_play_3_starburst,Starburst,netent,mobile_website,228fc2c2-f50e-4e3b-9bb3-83d40f309ade,paid,EUR,0,0,0.30,190.00,2024-04-02T00:20:20.293+00:00,2024-04-02T00:20:20.393+00:00

DATA;

    public function testImport(): void
    {
        $this->haveRates();

        /** @var GameVendor $gameVendor */
        $gameVendor = $this->haveRecord(GameVendors::class, [
            'name' => 'netent',
            'aliases' => ['netent'],
        ]);

        /** @var Game $game */
        $game = $this->haveRecord(Games::class, [
            'name' => 'Starburst',
            'vendor_id' => $gameVendor->id,
            'platform' => Game::PLATFORM_UNIVERSAL,
        ]);

        $this->haveRecord(UserGameRaws::class, [
            'site_id' => Site::VV,
            'spin_id' => '111',
            'user_id' => 1,
            'type' => 'bet',
            'game_id' => $game->id,
            'round_type' => UserGameRaw::ROUND_TYPE_PAID,
            'session_token' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade',
            'currency' => 'EUR',
            'real_amount' => '0.25',
            'real_amount_after' => '80.00',
            'bonus_amount' => '0.00',
            'bonus_amount_after' => '0.00',
            'created_at' => new \DateTimeImmutable('2024-04-02 00:00:03'),
        ]);
        $this->haveRecord(UserGameRaws::class, [
            'site_id' => Site::VV,
            'spin_id' => '112',
            'user_id' => 1,
            'type' => 'bet',
            'game_id' => $game->id,
            'round_type' => UserGameRaw::ROUND_TYPE_PAID,
            'session_token' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade',
            'currency' => 'EUR',
            'real_amount' => '0.25',
            'real_amount_after' => '100.00',
            'bonus_amount' => '0.00',
            'bonus_amount_after' => '0.00',
            'created_at' => new \DateTimeImmutable('2024-04-02 00:10:03'),
        ]);
        $this->haveRecord(UserGameRaws::class, [
            'site_id' => Site::VV,
            'spin_id' => '114',
            'user_id' => 1,
            'type' => 'win',
            'game_id' => $game->id,
            'round_type' => UserGameRaw::ROUND_TYPE_PAID,
            'session_token' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade',
            'currency' => 'EUR',
            'real_amount' => '10.00',
            'real_amount_after' => '100.00',
            'bonus_amount' => '0.00',
            'bonus_amount_after' => '0.00',
            'created_at' => new \DateTimeImmutable('2024-04-02 00:10:03'),
        ]);
        $this->haveRecord(UserGameRaws::class, [
            'site_id' => Site::VV,
            'spin_id' => '115',
            'user_id' => 1,
            'type' => 'win',
            'game_id' => $game->id,
            'round_type' => UserGameRaw::ROUND_TYPE_PAID,
            'session_token' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade',
            'currency' => 'EUR',
            'real_amount' => '20.00',
            'real_amount_after' => '100.00',
            'bonus_amount' => '0.00',
            'bonus_amount_after' => '0.00',
            'created_at' => new \DateTimeImmutable('2024-04-02 00:10:03'),
        ]);

        $this->runTask('update-users-games-aggregate', Res::VV, '2024-04-02', 'P1D');
        $this->seeRecord(UserGameTokens::class, ['site_id' => Site::VV, 'user_id' => 1, 'token_id' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade', 'after_amount' => '100.00', 'win_max' => '20.00']);

        $this->runTask('users-games-raw', 'VV', $this->debugFile(static::DATA));
        $this->runTask('update-users-games-aggregate', Res::VV, '2024-04-02', 'P1D');

        $this->seeRecord(UserGameTokens::class, ['site_id' => Site::VV, 'user_id' => 1, 'token_id' => '228fc2c2-f50e-4e3b-9bb3-83d40f309ade', 'after_amount' => '190.00', 'win_max' => '20.00']);
    }
}
