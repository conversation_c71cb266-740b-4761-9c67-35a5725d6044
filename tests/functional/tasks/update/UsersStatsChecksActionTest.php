<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\entities\Check;
use app\back\entities\Rate;
use app\back\entities\UserTransaction;
use app\back\modules\checks\checks\rules\UserTransactionsProductRule;
use app\back\modules\task\actions\update\checks\LyraQueryConditions;
use app\back\repositories\Checks;
use app\back\repositories\Users;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;

class UsersStatsChecksActionTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    private string $cmdResource;
    private string $cmdFromTime;
    private UserTransaction $targetStat;

    public function testMoreThenOneBonusPerDay(): void
    {
        $this->haveRates();
        [$this->cmdResource, $targetSiteId] = $this->randomResourceWithSiteId();
        $this->cmdFromTime = date('Y-m-d H:i:00', strtotime('-1 minute'));

        /** @see LyraQueryConditions */
        $userId = self::uniqRuntimeId();
        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 week')));
        $this->haveUserRecord(['site_id' => $targetSiteId, 'user_id' => $userId, 'date' => $dateReg]);

        $check = $this->haveCheckRecord([
            "source" => Check::SOURCE_USERS_STATS,
            "filters" => ["opId" => [UserTransaction::OP_ADMIN_IN_PRIZE]],
            "rules" => [[
                "id" => UserTransactionsProductRule::getClassId(),
                "metric" => UserTransactionsProductRule::METRIC_COUNT,
                "period" => 1,
                "periodUnit" => "day",
                "valueOperator" => ">",
                "value" => 1,
                "status" => [UserTransaction::STATUS_SUCCESS],
                "operation" => [UserTransaction::OP_ADMIN_IN_PRIZE],
            ]]
        ]);

        $this->targetStat = $this->haveUserTransactionRecord([
            'op_id' => UserTransaction::OP_ADMIN_IN_PRIZE,
            'status' => UserTransaction::STATUS_SUCCESS,
            'site_id' => $targetSiteId,
            'user_id' => $userId,
            'upserted_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        // first target stat. Count === 1. Check not triggered
        $this->checkAndAssertTriggers($check, 0);

        // other users OP_ADMIN_IN_PRIZE
        $this->haveUserTransactionRecord([
            'op_id' => UserTransaction::OP_ADMIN_IN_PRIZE,
            'status' => UserTransaction::STATUS_SUCCESS,
            'upserted_at' => $this->datetimeFromPeriod('-1 day +1 second', 'PT23H47M'),
        ]);
        // outdated OP_ADMIN_IN_PRIZE
        $this->createTargetUserStat([
            'op_id' => UserTransaction::OP_ADMIN_IN_PRIZE,
            'status' => UserTransaction::STATUS_SUCCESS,
            'upserted_at' => $this->datetimeFromPeriod("$this->cmdFromTime -2 day", 'P1D'),
        ]);
        // wrong op_id
        $this->createTargetUserStat([
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'upserted_at' => $this->datetimeFromPeriod('-1 day +1 second', 'PT23H47M'),
        ]);

        // wrong status
        $this->createTargetUserStat([
            'op_id' => UserTransaction::OP_ADMIN_IN_PRIZE,
            'status' => UserTransaction::STATUS_NEW,
            'upserted_at' => $this->datetimeFromPeriod('-1 day +1 second', 'PT23H47M'),
        ]);
        $this->checkAndAssertTriggers($check, 0);

        // Target stats count > 1. Check triggered
        $this->createTargetUserStat([
            'op_id' => UserTransaction::OP_ADMIN_IN_PRIZE,
            'status' => UserTransaction::STATUS_SUCCESS,
            'upserted_at' => $this->datetimeFromPeriod('-1 day +1 second', 'PT23H47M'),
        ]);

        // Target stats count > 1. Check triggered
        $this->checkAndAssertTriggers($check, 1);
    }

    public function testSourceValueFilter(): void
    {
        $this->haveRates();
        [$this->cmdResource, $targetSiteId] = $this->randomResourceWithSiteId();
        $this->cmdFromTime = date('Y-m-d H:i:00', strtotime('-1 minute'));
        $targetSum = 9.99;

        /** @see LyraQueryConditions */
        $userId = self::uniqRuntimeId();
        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 week')));
        $this->haveUserRecord(['site_id' => $targetSiteId, 'user_id' => $userId, 'date' => $dateReg]);

        $check = $this->haveCheckRecord([
            "source" => Check::SOURCE_USERS_STATS,
            "filters" => [
                'amountUsdOperator' => '==',
                'amountUsd' => (string) $targetSum,
            ],
        ]);

        $this->targetStat = $this->haveUserTransactionRecord([
            'site_id' => $targetSiteId,
            'user_id' => $userId,
            'upserted_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
            'amount_orig' => (string) ($targetSum + 0.01),
            'currency' => Rate::USD,
        ]);
        $this->checkAndAssertTriggers($check, 0);

        $this->createTargetUserStat([
            'site_id' => $targetSiteId,
            'user_id' => $userId,
            'upserted_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
            'amount_orig' => (string) $targetSum,
            'currency' => Rate::USD,
        ]);
        $this->checkAndAssertTriggers($check, 1);

        /// Old user doesn't trigger the check @see LyraQueryConditions
        $dateReg = date('Y-m-d H:i:00', strtotime('-60 day'));
        $this->db()->createCommand()->update(Users::TABLE_NAME, ['date' => $dateReg], ['site_id' => $targetSiteId, 'user_id' => $userId])->execute();
        $this->checkAndAssertTriggers($check, 1);
    }

    public function testNeedUserApprove(): void
    {
        $this->haveRates();
        [$this->cmdResource, $targetSiteId] = $this->randomResourceWithSiteId();
        $this->cmdFromTime = date('Y-m-d H:i:00', strtotime('-1 minute'));
        $check = $this->haveCheckRecord(["source" => Check::SOURCE_USERS_STATS]);

        /** @see LyraQueryConditions */
        $userId = self::uniqRuntimeId();
        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 week')));
        $this->haveUserRecord(['site_id' => $targetSiteId, 'user_id' => $userId, 'date' => $dateReg]);

        // check triggered, CheckUserRequest dont created
        $this->targetStat = $this->haveUserTransactionRecord([
            'site_id' => $targetSiteId,
            'user_id' => $userId,
            'upserted_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        $this->checkAndAssertTriggers($check, 1);

        // check triggered twice+prev, CheckUserRequest created
        $this->targetStat = $this->haveUserTransactionRecord([
            'site_id' => $targetSiteId,
            'user_id' => $userId,
            'upserted_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        $this->checkAndAssertTriggers($check, 3);
    }

    private function createTargetUserStat(array $data): void
    {
        $data['site_id'] = $this->targetStat->site_id;
        $data['user_id'] = $this->targetStat->user_id;
        $this->haveUserTransactionRecord($data);
    }

    private function checkAndAssertTriggers(Check $check, int $triggersCount): void
    {
        $this->runTask('update-checks-users-stats-lyra', $this->cmdResource, $this->cmdFromTime, 'PT1M');
        $this->seeRecordWithFields(Checks::class, ['id' => $check->id], ['trigger_count' => $triggersCount]);
    }
}
