<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\User;
use app\back\modules\task\actions\update\UsersStatusesNormalTask;
use app\back\repositories\Users;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersStatusesNormalTask::class)]
class UsersStatusesNormalTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD = '30';
    private const string MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD = '112.5';
    private const int HALF_MONTH_DAYS = 15; // period for ngr lt average calculation
    private const int VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK = 5; // prev month
    private const int VALID_USER_REG_DAYS_AGO = 10;
    private const int VALID_CLOUD_IN_OUT_RATE = 85;
    private const int VALID_ACTION_DAYS_AGO_FOR_DAILY_TASK = 1; // tomorrow

    //1. High to active by user. False: manual status
    public function testHighToActiveFalse(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 1,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_HIGH,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
            'is_manual_status' => true,
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO  / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user->user_id], ['active_status' => User::ACTIVE_STATUS_HIGH]);
    }

    //2. Active to low by user
    public function testActiveToLowByUser(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 2,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 1,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user->user_id], ['active_status' => User::ACTIVE_STATUS_LOW]);
    }

    // 3. Low to active
    public function testLowToActive(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user3 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 3,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_LOW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user3->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);
        $user4 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 3,
            'status' => User::STATUS_PRE_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        // invalid wd for cloud rate, but it doesn`t count because the false dep_last_at date
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user4->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK * 10 . ' day'))),
            'dep_lt_eur' => 0,
            'wd_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS)  * 10,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user3->user_id], ['active_status' => User::ACTIVE_STATUS_ACTIVE]);
    }

    // 4. Low to active. False by cloud rate
    public function testLowToActiveFalse(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user5 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 5,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_LOW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user5->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);
        $user6 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 5,
            'status' => User::STATUS_PRE_VIP,
            'active_status' => User::ACTIVE_STATUS_NEW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
            'currency' => null,
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user6->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS) * 2,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user5->user_id], ['active_status' => User::ACTIVE_STATUS_LOW]);
    }

    // 5. Active to low by cloud
    public function testActiveToLowByCloud(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user7 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 7,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user7->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);
        $user8 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 7,
            'status' => User::STATUS_PRE_VIP,
            'active_status' => User::ACTIVE_STATUS_NEW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user8->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS) * 2,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user7->user_id], ['active_status' => User::ACTIVE_STATUS_LOW]);
    }

    // 6. Low to high
    public function testLowToHigh(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user9 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 9,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_LOW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user9->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user9->user_id], ['active_status' => User::ACTIVE_STATUS_HIGH]);
    }

    // 7. High to active
    public function testHighToActive(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user11 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 11,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_HIGH,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user11->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user11->user_id], ['active_status' => User::ACTIVE_STATUS_ACTIVE]);
    }

    // 8. Active to active(instead low) by vip in cloud
    public function testActiveToActive(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user15 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 15,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user15->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);
        $user16 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 15,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_NEW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user16->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS) * 100,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user15->user_id], ['active_status' => User::ACTIVE_STATUS_ACTIVE]);
    }

    // 9. High to active(instead low) by vip in cloud
    public function testHighToActiveByVip(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user17 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 17,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_HIGH,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user17->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);
        $user18 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 17,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_NEW,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user18->user_id,
            'dep_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTIONS_DAYS_AGO_FOR_MONTHLY_TASK . ' day'))),
            'dep_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => (self::MIN_DEFAULT_HIGH_NGR_LT_AVG_EUR_FOR_PERIOD * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS) * 100,
        ]);

        $this->runTask('update-users-statuses-normal-monthly', Res::getResourceNameBySiteId($siteId));
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user17->user_id], ['active_status' => User::ACTIVE_STATUS_ACTIVE]);
    }

    // 10. Active to low by withdraw date
    public function testActiveToLowByWd(): void
    {
        $siteId = self::uniqSiteId();
        $this->haveRates();
        $this->haveUserStatusNormalThreshold();

        $user19 = $this->haveUserRecord([
            'site_id' => $siteId,
            'cid' => 19,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'date' => new \DateTimeImmutable(date('Y-m-d', strtotime('-' . self::VALID_USER_REG_DAYS_AGO . ' day'))),
            'country' => 'AL',
        ]);
        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $user19->user_id,
            'wd_last_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-' . self::VALID_ACTION_DAYS_AGO_FOR_DAILY_TASK . ' day'))),
            'dep_lt_eur' => ((self::MIN_DEFAULT_ACTIVE_NGR_LT_AVG_EUR_FOR_PERIOD - 1) * self::VALID_USER_REG_DAYS_AGO / self::HALF_MONTH_DAYS),
            'wd_lt_eur' => 0,
        ]);

        $this->runTask('update-users-statuses-normal-daily', Res::getResourceNameBySiteId($siteId), 'yesterday');
        $this->seeRecordWithFields(Users::class, ['site_id' => $siteId, 'user_id' => $user19->user_id], ['active_status' => User::ACTIVE_STATUS_LOW]);
    }
}
