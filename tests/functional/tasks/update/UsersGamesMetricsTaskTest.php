<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\components\helpers\DateHelper;
use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\UserMetric;
use app\back\modules\task\actions\update\UsersGamesMetricsTask;
use app\back\repositories\GameVendors;
use app\back\repositories\UserMetrics;
use app\back\repositories\UserSpecialInfos;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersGamesMetricsTask::class)]
class UsersGamesMetricsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testBetWinPlayRatio(): void
    {
        $this->haveRates();

        $siteId = Site::CV;
        $twoMonthsAgo = DateHelper::twoMonthsAgo();
        $userId1 = $this->haveUserRecord(['site_id' => $siteId, 'date' => $twoMonthsAgo])->user_id;
        $userId2 = $this->haveUserRecord(['site_id' => $siteId, 'date' => DateHelper::weekAgo()])->user_id;
        $this->haveRecords(UserSpecialInfos::class, [
            ['site_id' => $siteId, 'user_id' => $userId1],
            ['site_id' => $siteId, 'user_id' => $userId2],
        ]);
        $gameId = $this->haveGame('game')->id;

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => $gameId, 'bet_count' => 10, 'bet_amount' => '15', 'win_count' => 5,  'win_amount' => '30']);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => $gameId, 'bet_count' => 2, 'bet_amount' => '2', 'win_count' => 2,  'win_amount' => '2']);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => $gameId, 'bet_count' => 5, 'bet_amount' => '5', 'created_at' => DateHelper::twoWeeksAgo()]);
        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId1, 'game_id' => $gameId, 'bet_count' => 7, 'bet_amount' => '8', 'created_at' => $twoMonthsAgo]);

        $this->haveUserGameTokenRecord(['site_id' => $siteId, 'user_id' => $userId2, 'game_id' => $gameId, 'bet_count' => 1, 'bet_amount' => '1', 'created_at' => DateHelper::weekAgo()]); // Must be skipped

        $this->runTask('update-users-games-metrics', Res::CV, self::LAST_MINUTE_PERIOD);

        $expectedMetrics = [
            [UserMetric::M_BET_LT_COUNT, 24],
            [UserMetric::M_BET_LT_ORIG, '30.00'],
            [UserMetric::M_WIN_LT_COUNT, 7],
            [UserMetric::M_WIN_LT_ORIG, '32.00'],
        ];

        foreach ($expectedMetrics as [$metricConfig, $value]) {
            $this->seeRecordWithFields(UserMetrics::class, ['site_id' => $siteId, 'user_id' => $userId1, 'metric' => $metricConfig[0]], [$metricConfig[1] => $value]);
            $this->dontSeeRecord(UserMetrics::class, ['site_id' => $siteId, 'user_id' => $userId2, 'metric' => $metricConfig[0]]);
        }

        $this->seeRecordWithFields(UserSpecialInfos::class, ['site_id' => $siteId, 'user_id' => $userId1], ['play_ratio_lt' => number_format(3 / DateHelper::daysDiff($twoMonthsAgo, DateHelper::today()), 4)]);
        $this->seeRecordWithFields(UserSpecialInfos::class, ['site_id' => $siteId, 'user_id' => $userId2], ['play_ratio_lt' => null]);
    }

    public function testFavGamesAndVendors(): void
    {
        $this->haveRates();

        $siteId = Site::CV;
        $userId = $this->haveUserRecord(['date' => DateHelper::twoMonthsAgo()])->user_id;
        $this->haveRecord(GameVendors::class, ['name' => uniqid('', true)]); // Fake vendor to shift ids
        $vendorId1 = $this->haveRecord(GameVendors::class, ['name' => 'vendor1'])->id;
        $vendorId2 = $this->haveRecord(GameVendors::class, ['name' => 'vendor2'])->id;
        $gameId1 = $this->haveGame('game 1', $vendorId1)->id;
        $gameId2 = $this->haveGame('game 2', $vendorId2)->id;
        $gameId3 = $this->haveGame('game 3', $vendorId2)->id;

        $siteUser = ['site_id' => $siteId, 'user_id' => $userId];

        $this->haveUserGameTokenRecord(array_merge($siteUser, ['game_id' => $gameId1, 'bet_count' => 10]));
        $this->haveUserGameTokenRecord(array_merge($siteUser, ['game_id' => $gameId2, 'bet_count' => 5]));
        $this->haveUserGameTokenRecord(array_merge($siteUser, ['game_id' => $gameId3, 'bet_count' => 20]));
        $this->haveUserGameTokenRecord(array_merge($siteUser, ['game_id' => $gameId2, 'bet_count' => 30, 'win_amount' => '10', 'created_at' => DateHelper::twoWeeksAgo()]));
        $this->haveUserGameTokenRecord(array_merge($siteUser, ['game_id' => $gameId1, 'bet_count' => 70, 'win_amount' => '20', 'created_at' => DateHelper::twoMonthsAgo()]));

        $this->runTask('update-users-games-metrics', Res::CV, self::LAST_MINUTE_PERIOD);

        $expectedMetrics = [
            [UserMetric::M_FAV_GAME_7_1, $gameId3],
            [UserMetric::M_FAV_GAME_7_2, $gameId1],
            [UserMetric::M_FAV_GAME_7_3, $gameId2],
            [UserMetric::M_FAV_GAME_30_1, $gameId2],
            [UserMetric::M_FAV_GAME_30_2, $gameId3],
            [UserMetric::M_FAV_GAME_LT_1, $gameId1],
            [UserMetric::M_FAV_GAME_LT_2, $gameId2],
            [UserMetric::M_FAV_GAME_LT_3, $gameId3],
            [UserMetric::M_FAV_VENDOR_LT_1, $vendorId1],
            [UserMetric::M_FAV_VENDOR_30_1, $vendorId2],

            [UserMetric::M_BET_30_COUNT, 65],
            [UserMetric::M_WIN_30_ORIG, '10.00'],
        ];

        foreach ($expectedMetrics as [$metricConfig, $value]) {
            $this->seeRecordWithFields(UserMetrics::class, ['site_id' => $siteId, 'user_id' => $userId, 'metric' => $metricConfig[0]], [$metricConfig[1] => $value]);
        }
    }
}
