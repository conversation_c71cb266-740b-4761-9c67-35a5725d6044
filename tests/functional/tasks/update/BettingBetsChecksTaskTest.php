<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\BettingBet;
use app\back\entities\Check;
use app\back\entities\Rate;
use app\back\modules\checks\checks\filters\BettingBetsFilter;
use app\back\modules\task\actions\update\checks\LyraQueryConditions;
use app\back\repositories\CheckAlerts;
use app\back\repositories\Checks;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use Yiisoft\Db\Query\Query;

class BettingBetsChecksTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    private string $cmdResource;
    private string $cmdFromTime;

    public function testEachBetRefund200(): void
    {
        $this->haveRates();
        [$this->cmdResource, $targetSiteId] = $this->randomResourceWithSiteId(Res::PLATFORM_BETTING);
        $this->cmdFromTime = date('Y-m-d H:i:00', strtotime('-60 seconds'));
        $userId = self::uniqRuntimeId();
        /** @see LyraQueryConditions */
        $this->haveUserRecord([
            'user_id' => $userId,
            'site_id' => $targetSiteId,
            'date' => new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 week'))),
        ]);

        $check = $this->haveCheckRecord([
            "source" => Check::SOURCE_BETTING_BETS,
            "filters" => [
                "refundUsd" => 200,
                "refundUsdOperator" => BettingBetsFilter::OPERATOR_EQ,
                "status" => [BettingBet::STATUS_ROLLED_BACK],
            ]
        ]);

        $this->haveBettingBetRecord([
            'status' => BettingBet::STATUS_ROLLED_BACK,
            'user_id' => $userId,
            'site_id' => $targetSiteId,
            'refund_usd' => 199.99,
            'currency' => Rate::USD,
            'updated_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        $this->checkAndAssertTriggers($check, 0);

        $this->haveBettingBetRecord([
            'status' => BettingBet::STATUS_ROLLED_BACK,
            'user_id' => $userId,
            'site_id' => $targetSiteId,
            'refund_usd' => 200.01,
            'currency' => Rate::USD,
            'updated_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        $this->checkAndAssertTriggers($check, 0);
        self::assertEquals(0, $this->countCheckAlerts());

        $this->haveBettingBetRecord([
            'status' => BettingBet::STATUS_ROLLED_BACK,
            'user_id' => $userId,
            'site_id' => $targetSiteId,
            'refund_usd' => 200,
            'currency' => Rate::USD,
            'updated_at' => $this->datetimeFromPeriod($this->cmdFromTime, 'PT1M'),
        ]);
        $this->checkAndAssertTriggers($check, 1);
        self::assertEquals(1, $this->countCheckAlerts());
        $this->checkAndAssertTriggers($check, 2);
        self::assertEquals(1, $this->countCheckAlerts());
    }

    private function checkAndAssertTriggers(Check $check, int $triggersCount): void
    {
        $this->runTask('update-checks-betting-bets-lyra', $this->cmdResource, $this->cmdFromTime, 'PT1M');
        $this->seeRecordWithFields(Checks::class, ['id' => $check->id], ['trigger_count' => $triggersCount]);
    }

    private function countCheckAlerts(): int
    {
        return (new Query($this->db()))->from(CheckAlerts::TABLE_NAME)->count();
    }
}
