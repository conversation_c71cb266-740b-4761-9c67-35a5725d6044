<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\BettingLog;
use app\back\entities\Site;
use app\back\entities\UserGameToken;
use app\back\repositories\BettingLogs;
use app\back\repositories\UserGameTokens;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;

class UsersGamesBettingActionTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testImport(): void
    {
        $this->haveRates();
        $siteId = Site::GGB;

        $this->haveRecords(BettingLogs::class, [
            [
                "id" => '1',
                "bet_id" => '1',
                "site_id" => $siteId,
                "user_id" => 113,
                "created_at" => new \DateTimeImmutable('2020-10-10 14:53:12'),
                "type" => BettingLog::TYPE_SETTLE_REFUND,
                'auth_token' => '1',
                "currency" => 'RUB',
                "real_amount" => '35000.00',
                "bonus_amount" => '0.00',
                "real_amount_usd" => '455.93',
                "real_amount_eur" => '385.37',
                "real_amount_rub" => '35000.00',
                "bonus_amount_usd" => '0.00',
                "bonus_amount_eur" => '0.00',
                "bonus_amount_rub" => '0.00',
            ],
            [
                "id" => '2',
                "bet_id" => '1',
                "site_id" => $siteId,
                "user_id" => 113,
                "created_at" => new \DateTimeImmutable('2020-10-10 13:07:06'),
                "type" => BettingLog::TYPE_PLACE,
                'auth_token' => '1',
                "currency" => 'RUB',
                "real_amount" => '0.00',
                "bonus_amount" => '0.00',
                "real_amount_usd" => '-455.93',
                "real_amount_eur" => '-385.37',
                "real_amount_rub" => '-35000.00',
                "bonus_amount_usd" => '0.00',
                "bonus_amount_eur" => '0.00',
                "bonus_amount_rub" => '0.00',
            ],
            [
                "id" => '3',
                "bet_id" => '2',
                "site_id" => $siteId,
                "user_id" => 115,
                "created_at" => new \DateTimeImmutable('2020-10-10 14:53:12'),
                "type" => BettingLog::TYPE_SETTLE_WIN,
                'auth_token' => '1',
                "currency" => 'RUB',
                "real_amount" => '35000.00',
                "bonus_amount" => '0.00',
                "real_amount_usd" => '455.93',
                "real_amount_eur" => '385.37',
                "real_amount_rub" => '35000.00',
                "bonus_amount_usd" => '0.00',
                "bonus_amount_eur" => '0.00',
                "bonus_amount_rub" => '0.00',
            ],
            [
                "id" => '4',
                "bet_id" => '2',
                "site_id" => $siteId,
                "user_id" => 115,
                "created_at" => new \DateTimeImmutable('2020-10-09 13:07:06'),
                "type" => BettingLog::TYPE_PLACE,
                'auth_token' => '1',
                "currency" => 'RUB',
                "real_amount" => '-35000.00',
                "bonus_amount" => '0.00',
                "real_amount_usd" => '-455.93',
                "real_amount_eur" => '-385.37',
                "real_amount_rub" => '-35000.00',
                "bonus_amount_usd" => '0.00',
                "bonus_amount_eur" => '0.00',
                "bonus_amount_rub" => '0.00',
            ],
            [
                "id" => '5',
                "bet_id" => '3',
                "site_id" => $siteId,
                "user_id" => 116,
                "created_at" => new \DateTimeImmutable('2020-10-10 13:07:46'),
                "type" => BettingLog::TYPE_CASH_OUT,
                'auth_token' => '1',
                "currency" => 'USD',
                "real_amount" => '10.00',
                "bonus_amount" => '0.00',
                "real_amount_usd" => '10.00',
                "real_amount_eur" => '9.00',
                "bonus_amount_usd" => '0.00',
                "bonus_amount_eur" => '0.00',
            ],
            [
                "id" => '6',
                "bet_id" => '3',
                "site_id" => $siteId,
                "user_id" => 116,
                "created_at" => new \DateTimeImmutable('2020-10-10 13:08:06'),
                "type" => BettingLog::TYPE_UNSETTLE,
                'auth_token' => '1',
                "currency" => 'USD',
                "real_amount" => '-510.00',
                "bonus_amount" => '-100.00',
                "real_amount_usd" => '-510.00',
                "real_amount_eur" => '-459.00',
                "bonus_amount_usd" => '-100.00',
                "bonus_amount_eur" => '-90.00',
            ],
        ]);

        $this->runTask('update-users-games-from-betting-log', Res::GGB, '2020-10-10', 'P1D');

        $this->seeRecordWithFields(UserGameTokens::class, ['site_id' => $siteId, 'user_id' => 113], [
            'created_at' => new \DateTimeImmutable('2020-10-10 13:07:06'),
            'last_action_at' => new \DateTimeImmutable('2020-10-10 14:53:12'),
            'bet_count' => 1,
            'win_count' => 0,
            'bet_amount' => '0.00',
            'win_amount' => '35000.00',
            'currency' => 'RUB',
            'bet_amount_usd' => '0.00',
            'win_amount_usd' => '500.00',
            'balance_type' => UserGameToken::BALANCE_TYPE_REAL,
            'bet_amount_eur' => '0.00',
            'win_amount_eur' => '450.00',
            'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS,
            "after_amount" => '35000.00',
            "after_amount_usd" => '500.00',
            "after_amount_eur" => '450.00',
        ]);

        $this->seeRecordWithFields(UserGameTokens::class, ['site_id' => $siteId, 'user_id' => 115], [
            'created_at' => new \DateTimeImmutable('2020-10-09 13:07:06'),
            'last_action_at' => new \DateTimeImmutable('2020-10-10 14:53:12'),
            'bet_count' => 1,
            'win_count' => 1,
            'bet_amount' => '35000.00',
            'win_amount' => '35000.00',
            'currency' => 'RUB',
            'bet_amount_usd' => '500.00',
            'win_amount_usd' => '500.00',
            'balance_type' => UserGameToken::BALANCE_TYPE_REAL,
            'bet_amount_eur' => '450.00',
            'win_amount_eur' => '450.00',
            'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS,
            "after_amount" => '35000.00',
            "after_amount_usd" => '500.00',
            "after_amount_eur" => '450.00',
        ]);

        $this->seeRecordWithFields(UserGameTokens::class, ['site_id' => $siteId, 'user_id' => 116, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL], [
            'created_at' => new \DateTimeImmutable('2020-10-10 13:07:46'),
            'last_action_at' => new \DateTimeImmutable('2020-10-10 13:08:06'),
            'bet_count' => 1,
            'win_count' => 0,
            'bet_amount' => '510.00',
            'win_amount' => '10.00',
            'currency' => 'USD',
            'bet_amount_usd' => '510.00',
            'win_amount_usd' => '10.00',
            'bet_amount_eur' => '459.00',
            'win_amount_eur' => '9.00',
            'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS,
            "after_amount" => '-510.00',
            "after_amount_usd" => '-510.00',
            "after_amount_eur" => '-459.00',
        ]);

        $this->seeRecordWithFields(UserGameTokens::class, ['site_id' => $siteId, 'user_id' => 116, 'balance_type' => UserGameToken::BALANCE_TYPE_BONUS], [
            'created_at' => new \DateTimeImmutable('2020-10-10 13:07:46'),
            'last_action_at' => new \DateTimeImmutable('2020-10-10 13:08:06'),
            'bet_count' => 1,
            'win_count' => 0,
            'bet_amount' => '100.00',
            'win_amount' => null,
            'currency' => 'USD',
            'bet_amount_usd' => '100.00',
            'win_amount_usd' => null,
            'bet_amount_eur' => '90.00',
            'win_amount_eur' => null,
            'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS,
            "after_amount" => '-100.00',
            "after_amount_usd" => '-100.00',
            "after_amount_eur" => '-90.00',
        ]);

        $this->runTask('update-users-games-from-betting-log', Res::VV, '2020-10-10', 'P1D');

        $this->dontSeeRecord(UserGameTokens::class, ['site_id' => $siteId, 'user_id' => 120]);
    }
}
