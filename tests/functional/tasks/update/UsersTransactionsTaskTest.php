<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\checks\LyraQueryConditions;
use app\back\modules\task\actions\update\UsersTransactionsFirstsTask;
use app\back\modules\task\actions\update\UsersTransactionsMetricsIncrementalTask;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[
    CoversClass(UsersTransactionsFirstsTask::class),
    CoversClass(UsersTransactionsMetricsIncrementalTask::class),
]
class UsersTransactionsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testIsFirst(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $this->haveRates();

        $userStat1 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-01-14 22:44:00'),
            'updated_at' => new \DateTimeImmutable('2020-01-14 22:44:10'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_NEW,
        ]);
        $userStat2 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-01-14 22:44:20'),
            'updated_at' => new \DateTimeImmutable('2020-01-14 22:45:00'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_FAIL,
        ]);
        $userStat3 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-01-14 22:47:00'),
            'updated_at' => new \DateTimeImmutable('2020-01-14 22:47:10'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_FAIL,
        ]);
        $userStat4 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-01-14 22:46:50'),
            'updated_at' => new \DateTimeImmutable('2020-01-14 22:48:21'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);
        $userStat5 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('2020-01-14 22:46:50'),
            'updated_at' => new \DateTimeImmutable('2020-01-14 22:48:21'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_NEW,
        ]);
        /** @see LyraQueryConditions */
        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('-1 week')));
        $this->haveUserRecord(['site_id' => $siteId, 'user_id' => $userId, 'date' => $dateReg]);

        $this->runTask('update-users-transactions-firsts', 'CV', '2020-01-14', 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat1->site_id,
            'transaction_id' => $userStat1->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => false,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat2->site_id,
            'transaction_id' => $userStat2->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => false,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat3->site_id,
            'transaction_id' => $userStat3->transaction_id,
        ], [
            'is_first_try' => false,
            'is_first_success' => false,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat4->site_id,
            'transaction_id' => $userStat4->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => true,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat5->site_id,
            'transaction_id' => $userStat5->transaction_id,
        ], [
            'is_first_try' => false,
            'is_first_success' => false,
        ]);
    }

    public function testLaterUpdatedDate(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $d = '2024-01-18';
        $this->haveRates();

        $userStat1 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 02:21:00"),
            'updated_at' => new \DateTimeImmutable("$d 03:06:00"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);

        $userStat2 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 02:27:00"),
            'updated_at' => new \DateTimeImmutable("$d 03:13:00"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_FAIL,
        ]);

        $userStat3 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable("$d 02:30:00"),
            'updated_at' => new \DateTimeImmutable("$d 02:30:00"),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);

        /** @see LyraQueryConditions */
        $dateReg = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime($d)));
        $this->haveUserRecord(['site_id' => $siteId, 'user_id' => $userId, 'date' => $dateReg]);

        $this->runTask('update-users-transactions-firsts', 'CV', $d, 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat1->site_id,
            'transaction_id' => $userStat1->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => false,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat2->site_id,
            'transaction_id' => $userStat2->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => false,
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $userStat3->site_id,
            'transaction_id' => $userStat3->transaction_id,
        ], [
            'is_first_try' => true,
            'is_first_success' => true,
        ]);
    }

    public function testMedian(): void
    {
        $this->haveRates();

        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $dateFrom = '2023-08-16 00:00:00';

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '84.05',
            'amount_orig' => '500.00',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+10 sec', strtotime($dateFrom)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '8405.22',
            'amount_orig' => '50000.00',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+20 sec', strtotime($dateFrom)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', $dateFrom, 'PT1M');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_median_eur' => '4244.64',
            'dep_median_orig' => '25250.00',
        ]);

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '168.10',
            'amount_orig' => '1000.00',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+90 sec', strtotime($dateFrom)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', $dateFrom, 'PT2M');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_median_eur' => '168.10',
            'dep_median_orig' => '1000.00',
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', '2023-11-17', 'PT2M');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_median_orig' => null,
            'dep_median_eur' => null,
        ]);
    }

    public function testMetrics(): void
    {
        $this->haveRates();

        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $dateFrom = '2023-10-20 00:00:00';

        $stat1 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '20.05',
            'amount_orig' => '30.35',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+10 sec', strtotime($dateFrom)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'pay_sys_id' => 1,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $this->runTask('update-users-transactions-metrics-incremental', 'CV', $dateFrom, 'PT1H');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_lt_eur' => $stat1->amount_eur,
            'dep_lt_orig' => $stat1->amount_orig,
            'dep_last_at' => $stat1->updated_at,
            'dep_lt_count' => 1
        ]);

        $dateFrom2 = '2023-10-20 01:00:00';

        $stat2 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '10.00',
            'amount_orig' => '15.00',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+10 min', strtotime($dateFrom2)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'pay_sys_id' => 1,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->runTask('update-users-transactions-metrics-incremental', 'CV', $dateFrom2, 'PT1H');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_lt_eur' => (string)($stat1->amount_eur + $stat2->amount_eur),
            'dep_lt_orig' => (string)($stat1->amount_orig + $stat2->amount_orig),
            'dep_last_at' => $stat2->updated_at,
            'dep_lt_count' => 2,
        ]);

        $dateFrom3 = '2023-10-20 02:00:00';

        $stat3 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '25.00',
            'amount_orig' => '40.10',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+10 min', strtotime($dateFrom3)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'pay_sys_id' => 2,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '5.00',
            'amount_orig' => '10.10',
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('+30 min', strtotime($dateFrom3)))),
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_FAIL,
            'pay_sys_id' => 2,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->runTask('update-users-transactions-metrics-incremental', 'CV', $dateFrom3, 'PT1H');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_lt_eur' => (string)($stat1->amount_eur + $stat2->amount_eur + $stat3->amount_eur),
            'dep_lt_orig' => (string)($stat1->amount_orig + $stat2->amount_orig + $stat3->amount_orig),
            'dep_last_at' => $stat3->updated_at,
            'dep_lt_count' => 3
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', $dateFrom, 'P1D');

        $this->seeRecordWithFields(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
        ], [
            'dep_lt_eur' => (string)($stat1->amount_eur + $stat2->amount_eur + $stat3->amount_eur),
            'dep_lt_orig' => (string)($stat1->amount_orig + $stat2->amount_orig + $stat3->amount_orig),
            'dep_last_at' => $stat3->updated_at,
            'dep_lt_count' => 3,
            'pay_success_ratio_lt' => "0.7500",
            'favorite_pay_sys_id' => 1
        ]);
    }

    public function testExtType(): void
    {
        $this->haveRates();

        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $dateFrom = '2023-10-20';

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '20.00',
            'amount_orig' => '30.00',
            'updated_at' => "$dateFrom 00:00:10",
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'pay_sys_id' => 1,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL
        ]);

        $this->haveUserSpecialInfo([
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $this->runTask('update-users-transactions-metrics-incremental', 'CV', $dateFrom, 'PT1H');

        $this->seeRecord(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
            'dep_lt_count' => 1,
            'dep_last_at' => "$dateFrom 00:00:10",
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', $dateFrom, 'P1D');

        $this->seeRecord(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
            'dep_lt_count' => 1,
            'dep_last_at' => "$dateFrom 00:00:10",
            'pay_bonus_count_ratio_lt' => '0.0000'
        ]);

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_eur' => '20.00',
            'amount_orig' => '30.00',
            'updated_at' => "$dateFrom 00:00:20",
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'pay_sys_id' => 1,
            'ext_type' => UserTransaction::EXT_TYPE_PRIZE
        ]);

        $this->runTask('update-users-transactions-metrics-incremental', 'CV', $dateFrom, 'PT1H');

        $this->seeRecord(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
            'dep_lt_count' => 1,
            'dep_last_at' => "$dateFrom 00:00:10",
        ]);

        $this->runTask('update-users-transactions-metrics-full', 'CV', $dateFrom, 'P1D');

        $this->seeRecord(UserSpecialInfos::class, [
            'user_id' => $userId,
            'site_id' => $siteId,
            'dep_lt_count' => 1,
            'dep_last_at' => "$dateFrom 00:00:10",
            'pay_bonus_count_ratio_lt' => '1.0000'
        ]);
    }
}
