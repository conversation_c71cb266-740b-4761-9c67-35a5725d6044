<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\components\Console;
use app\back\components\ConsoleTable;
use app\back\components\helpers\Arr;
use app\back\components\PgArray;
use app\back\components\SiteUserBuilder;
use app\back\config\tasks\Res;
use app\back\entities\Requisite;
use app\back\entities\S2pOrder;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentFace;
use app\back\entities\UserTransaction;
use app\back\repositories\Requisites;
use app\back\repositories\S2pOrders;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\UserDocumentFaceSimilarQueues;
use app\back\repositories\UserDocuments;
use app\back\repositories\UserRequisites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use Yiisoft\Db\Query\Query;

class CloudTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testJoin(): void
    {
        $this->haveRates();

        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789011'], $user1);
        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789011'], $user2);
        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789011'], $user3);
        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789012'], $user4);
        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789012'], $user5);
        $this->runTasks();
        $this->assertUserClouds(
            [$user1, $user2, $user3],
            [$user4, $user5],
        );

        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789013'], $user6);
        $this->runTasks();
        $this->assertUserClouds(
            [$user1, $user2, $user3],
            [$user4, $user5],
            [$user6],
        );

        $this->haveS2pOrderWithUserRecord(['requisite' => 'Z123456789011'], $user4);
        $this->repo(Users::class)->resetCloudSources($user4->site_id, $user4->user_id);
        $this->runTasks();
        $this->assertUserClouds(
            [$user1, $user2, $user3, $user4, $user5],
            [$user6],
        );
    }

    public function testS2pCardReplaceWithTokenizedCard(): void
    {
        $cardNum = '12345678****9876/12/2048';
        $cardToken = "af3";
        $cardTokenSecond = "21b";
        $cardOther = '12345678****9876/12/2096';
        $cardTokenOther = "fff";
        $this->haveRates();
        $reqOtherNum = 'z123456789011';

        $order0 = $this->haveS2pOrderWithUserRecord(['requisite' => $reqOtherNum], $user1);
        $order1 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum], $user1);
        $order2 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum], $user2);
        $order3 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum], $user3);
        $order4 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum], $user4);
        $order5 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum], $user5);
        $orderOther1 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardOther], $userOther);


        $this->runTasks();
        /** @var Requisite $requisiteCardPlain */
        $requisiteCardPlain = $this->seeRecord(Requisites::class, ['requisite' => $cardNum]);
        /** @var Requisite $requisiteOther */
        $requisiteOther = $this->seeRecord(Requisites::class, ['requisite' => $reqOtherNum]);
        // fake requisite with value same as card
        $requisiteOther->requisite = $requisiteCardPlain->requisite;
        $this->repo(Requisites::class)->update($requisiteOther, ['requisite']);

        /** @var Requisite $requisiteCardPlainOther */
        $requisiteCardPlainOther = $this->seeRecord(Requisites::class, ['requisite' => $cardOther]);
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order1, $order2, $order3, $order4, $order5],
            $requisiteCardPlainOther->id => [$orderOther1],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);


        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order11 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user1);
        $orderOther2 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardOther, 'requisite_token' => $cardTokenOther], $userOther);
        $this->runTasks();
        /** @var Requisite $requisiteCardTokenized */
        $requisiteCardTokenized = $this->seeRecord(Requisites::class, ['requisite' => "$cardNum#$cardToken"]);
        /** @var Requisite $requisiteCardTokenizedOther */
        $requisiteCardTokenizedOther = $this->seeRecord(Requisites::class, ['requisite' => "$cardOther#$cardTokenOther"]);
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order1, $order2, $order3, $order4, $order5],
            $requisiteCardTokenized->id => [$order11],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);


        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order21 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user2);
        $this->runTasks();
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order2, $order3, $order4, $order5],
            $requisiteCardTokenized->id => [$order1, $order11, $order21],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);

        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order22 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user2);
        $this->runTasks();
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order2, $order3, $order4, $order5],
            $requisiteCardTokenized->id => [$order1, $order11, $order21, $order22],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);


        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order41 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardTokenSecond], $user4);
        $this->runTasks();
        /** @var Requisite $requisiteCardTokenizedSecond */
        $requisiteCardTokenizedSecond = $this->seeRecord(Requisites::class, ['requisite' => "$cardNum#$cardTokenSecond"]);
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order2, $order3, $order4, $order5],
            $requisiteCardTokenized->id => [$order1, $order11, $order21, $order22],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
            $requisiteCardTokenizedSecond->id => [$order41],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);


        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order51 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardTokenSecond], $user5);
        $this->runTasks();
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardPlain->id => [$order2, $order3, $order5],
            $requisiteCardTokenized->id => [$order1, $order11, $order21, $order22],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
            $requisiteCardTokenizedSecond->id => [$order4, $order41, $order51],
        ]);
        $this->assertUserClouds([$user1, $user2, $user3, $user4, $user5], [$userOther]);


        $this->shiftDatetimeDbColumn(S2pOrders::TABLE_NAME, 'date', '-P3D');
        $order31 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user3);
        $this->runTasks();
        $this->assetOrderAndUserRequisites([
            $requisiteOther->id => [$order0],
            $requisiteCardTokenized->id => [$order1, $order11, $order2, $order21, $order22, $order3, $order31],
            $requisiteCardTokenizedOther->id => [$orderOther1, $orderOther2],
            $requisiteCardTokenizedSecond->id => [$order4, $order5, $order41, $order51],
        ]);
        $this->assertUserClouds(
            [$user1, $user2, $user3],
            [$user4, $user5],
            [$userOther],
        );
    }

    public function testTokenizedCardFromStatToS2pOrders(): void
    {
        $cardNum = '12345678****9876/12/2048';
        $cardToken = "af3";
        $this->haveRates();

        $stat1 = $this->haveUserStatRecordWithUserRecord(['wallet' => $cardNum,], $user1);
        $this->runTask('update-users-requisites-from-users-transactions', Res::GGB, '-1 day', 'P2D');
        /** @var Requisite $requisiteCardPlain */
        $requisiteCardPlain = $this->seeRecord(Requisites::class, ['requisite' => $cardNum]);
        $this->assetOrderAndUserRequisites([
            $requisiteCardPlain->id => [$stat1],
        ]);

        $order1 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user1);
        $this->runTask('update-users-requisites-from-s2p-orders', Res::S2P, '-1 day', 'P2D');
        /** @var Requisite $requisiteCardTokenized */
        $requisiteCardTokenized = $this->seeRecord(Requisites::class, ['requisite' => "$cardNum#$cardToken"]);

        $this->assetOrderAndUserRequisites([
            $requisiteCardTokenized->id => [$stat1, $order1],
        ]);
    }

    public function testTokenizedCardFromS2pOrdersToStat(): void
    {
        $cardNum = '12345678****9876/12/2048';
        $cardToken = "af3";
        $this->haveRates();

        $order1 = $this->haveS2pOrderWithUserRecord(['requisite' => $cardNum, 'requisite_token' => $cardToken], $user1);
        $this->runTask('update-users-requisites-from-s2p-orders', Res::S2P, '-1 day', 'P2D');
        /** @var Requisite $requisiteCardTokenized */
        $requisiteCardTokenized = $this->seeRecord(Requisites::class, ['requisite' => "$cardNum#$cardToken"]);
        $this->assetOrderAndUserRequisites([
            $requisiteCardTokenized->id => [$order1],
        ]);

        $stat1 = $this->haveUserStatRecordWithUserRecord(['wallet' => $cardNum,], $user1);
        $this->runTask('update-users-requisites-from-users-transactions', Res::GGB, '-1 day', 'P2D');
        $this->dontSeeRecord(Requisites::class, ['requisite' => $cardNum]);

        $this->assetOrderAndUserRequisites([
            $requisiteCardTokenized->id => [$order1, $stat1],
        ]);
    }

    private function assertUserClouds(array ...$clouds): void
    {
        $cloudUsers = array_map(static fn(User $u) => [
            'site_id' => $u->site_id,
            'user_id' => $u->user_id
        ], array_merge(...$clouds));

        $cloudUsers = (new Query($this->db()))
            ->select([
                'site_id',
                'user_id',
                'cid'
            ])
            ->from(Users::TABLE_NAME)
            ->where(['IN', ['site_id', 'user_id'], $cloudUsers])
            ->orderBy('user_id')
            ->all();

        static $builder;
        $builder ??= $this->container()->get(SiteUserBuilder::class);

        array_walk_recursive($clouds, static fn(User &$u) => $u = $builder->siteUserToValue($u->site_id, $u->user_id));
        array_walk($cloudUsers, static fn(&$arr) => $arr['site_user'] = $builder->siteUserToValue($arr['site_id'], $arr['user_id']));
        $cloudActual = array_values(Arr::groupBy($cloudUsers, ['cid'], value: 'site_user'));
        $expectedStr = implode("\n\t", array_map(static fn($l) => implode(',', $l), $clouds));
        $actualStr = implode("\n\t", array_map(static fn($l) => implode(',', $l), $cloudActual));

        self::assertEquals($clouds, $cloudActual, "Clouds is not equal\nExpected:\n\t$expectedStr\nActual:\n\t$actualStr");
    }

    private function runTasks($runRequisitesFromUserTransactions = false): void
    {
        $this->runTask('update-users-requisites-from-s2p-orders', Res::S2P, '-1 day', 'P2D');
        $runRequisitesFromUserTransactions && $this->runTask('update-users-requisites-from-users-transactions', Res::GGB, '-1 day', 'P2D');
        $this->runTask('update-users-clouds-sources', Res::CID, '-1 day', 'P2D');

        static $cidResetQuery;
        $cidResetQuery ??= (new Query($this->db()))
            ->from(Users::TABLE_NAME)
            ->where(['IS NOT', 'cid_reset_at', null]);

        $i = 0;
        // to complete processing self reset clouds
        while ($cidResetQuery->count() > 0) {
            $i++ > 3 && self::fail('Reset cloud recursion');
            $this->shiftDatetimeDbColumn(Users::TABLE_NAME, 'cid_reset_at', '-PT1S');
            $this->runTask('update-users-cids', Res::CID, '-1 day', 'P2D', '--mode=quick');
        }
    }

    private function haveS2pOrderWithUserRecord(array $props, ?User &$user = null): S2pOrder
    {
        $user ??= $this->haveUserRecord([
            'site_id' => Site::GGB,
        ]);
        // trim milliseconds and shift 1 minute
        $created = new \DateTimeImmutable(date('Y-m-d H:i:s', strtotime('-1 minute')));
        return $this->haveS2pOrderRecord([
            ...$props,
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'status' => S2pOrder::STATUS_SUCCESS,
            'date_created' => $created,
            'date' => $created,
        ]);
    }

    private function haveUserStatRecordWithUserRecord(array $props, ?User &$user = null): UserTransaction
    {
        $user ??= $this->haveUserRecord([
            'site_id' => Site::GGB,
        ]);
        return $this->haveUserTransactionRecord([
            ...$props,
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'updated_at' => new \DateTimeImmutable(date('Y-m-d H:i:s'))
        ]);
    }

    private function assetOrderAndUserRequisites(array $expectations): void
    {
        ksort($expectations);
        $expectedUserRequisites = $expectedOrderRequisites = $expectedStatRequisites = [];
        foreach ($expectations as $requisiteId => $ordersOrStats) {
            $expectedUserRequisites[$requisiteId] = array_unique(array_map(static fn(S2pOrder | UserTransaction $o) => "$o->site_id-$o->user_id", $ordersOrStats));
            $orders = array_filter($ordersOrStats, static fn($o) => $o instanceof S2pOrder);
            $expectedOrderRequisites[$requisiteId] = array_map(static fn(S2pOrder $o) => "$o->site_id-$o->id", $orders);
            $transactions = array_filter($ordersOrStats, static fn($s) => $s instanceof UserTransaction);
            $expectedStatRequisites[$requisiteId] = array_map(static fn(UserTransaction $s) => "$s->site_id-$s->transaction_id", $transactions);
            self::assertEmpty(
                array_filter($ordersOrStats, static fn($e) => !($e instanceof UserTransaction || $e instanceof S2pOrder)),
                'Not supported objects to check requisite'
            );
            sort($expectedUserRequisites[$requisiteId]);
            sort($expectedOrderRequisites[$requisiteId]);
            sort($expectedStatRequisites[$requisiteId]);
        }

        $expectedOrderRequisites = array_filter($expectedOrderRequisites, static fn($e) => !empty($e));
        $expectedStatRequisites = array_filter($expectedStatRequisites, static fn($e) => !empty($e));


        $userRequisites = (new Query($this->db()))
            ->from(UserRequisites::TABLE_NAME)
            ->orderBy(['requisite_id' => SORT_ASC, 'site_id' => SORT_ASC, 'user_id' => SORT_ASC])
            ->all();
        $actualUserRequisites = [];
        foreach ($userRequisites as ['site_id' => $siteId, 'user_id' => $userId, 'requisite_id' => $requisiteId]) {
            $actualUserRequisites[$requisiteId][] = "$siteId-$userId";
        }
        array_walk($actualUserRequisites, static fn(&$l) => sort($l));
        self::assertSame(
            $expectedUserRequisites,
            $actualUserRequisites,
            "Order requisites expectations failed\n" .
            "Expected:\n{$this->arrayToStr($expectedUserRequisites)}\n" .
            "Actual:\n{$this->arrayToStr($actualUserRequisites)}"
        );


        if (!empty($expectedOrderRequisites)) {
            $orders = (new Query($this->db()))
                ->from(S2pOrders::TABLE_NAME)
                ->orderBy(['requisite_id' => SORT_ASC, 'site_id' => SORT_ASC, 'id' => SORT_ASC])
                ->all();
            $actualOrderRequisites = [];
            foreach ($orders as ['site_id' => $siteId, 'id' => $orderId, 'requisite_id' => $requisiteId]) {
                $actualOrderRequisites[$requisiteId][] = "$siteId-$orderId";
            }
            array_walk($actualOrderRequisites, static fn(&$l) => sort($l));
            self::assertSame(
                $expectedOrderRequisites,
                $actualOrderRequisites,
                "User requisites expectations failed\n" .
                "Expected:\n{$this->arrayToStr($expectedOrderRequisites)}\n" .
                "Actual:\n{$this->arrayToStr($actualOrderRequisites)}"
            );
        }


        if ($expectedStatRequisites) {
            $transactions = (new Query($this->db()))
                ->from(UserTransactions::TABLE_NAME)
                ->orderBy(['requisite_id' => SORT_ASC])
                ->all();
            $actualStatsRequisites = [];
            foreach ($transactions as ['site_id' => $siteId, 'transaction_id' => $transactionId, 'requisite_id' => $requisiteId]) {
                $actualStatsRequisites[$requisiteId][] = "$siteId-$transactionId";
            }
            array_walk($actualStatsRequisites, static fn(&$l) => sort($l));
            self::assertSame(
                $expectedStatRequisites,
                $actualStatsRequisites,
                "Stat requisites expectations failed\n" .
                "Expected:\n{$this->arrayToStr($expectedStatRequisites)}\n" .
                "Actual:\n{$this->arrayToStr($actualStatsRequisites)}"
            );
        }
    }

    private function arrayToStr(array $a): string
    {
        $str = [];
        if (empty($a)) {
            return '[empty]';
        }
        foreach ($a as $k => $v) {
            $str[] = $k . ' => ' . implode(', ', $v);
        }

        return implode("\n", $str);
    }

    #[DataProvider('similarityFaceTaskDataProvider')]
    public function testSimilarFacesTask(string $cloudsBefore, string $similarities, string $cloudsAfter, int ...$queuedFaces): void
    {
        $faces = $this->haveFaces(5);
        $this->setSimilarIdPerGroup(...$this->groupFacesByCloudStr($cloudsBefore, $faces));
        $this->insertSimilarityPerGroup(...$this->groupFacesByCloudStr($similarities, $faces));
        $this->enqueueSimilarIdsAndRunTask(...array_map(static fn($n) => $faces[$n - 1], $queuedFaces));
        $this->assertFaceClouds($cloudsAfter, $faces);
    }

    public static function similarityFaceTaskDataProvider(): array
    {
        return [
            // $cloudsBefore,   $similarities,         $cloudsAfter    $queuedFaces
            [  '1 2 3 4 5',     '',                    '1 2 3 4 5',               ],
            [  '1 2 3 4',       '',                    '1 2 3 4 5',               ],
            [  '1+3 2 4 5',     '1+2',                 '1+2 3 4 5',    1, 2       ],
            [  '1+3',           '1+2',                 '1+2 3 4 5',    1          ],
            [  '1+3',           '1+2',                 '1+2 3 4 5',    3          ],
            [  '1+2 4+5',       '1+2 4+5 1+3 3+5',     '1+2+3+4+5',               ],
            [  '1',             '1+2',                 '1+2 3 4 5'                ],
            [  '1 2 3 4',       '4+5',                 '1 2 3 4+5'                ],
            [  '1+2',           '',                    '1 2 3 4 5',    1          ],
            [  '1+2 3 4 5',     '',                    '1 2 3 4 5',    1          ],
            [  '1+2 3 4 5',     '2+3',                 '1 2+3 4 5',    1          ],
            [  '1 2+3+4+5',     '1+2 2+5 3+4 4+5',     '1+2+3+4+5',    5          ],
            [  '1+2+3+4+5',     '',                    '1 2 3 4 5',    5          ],
            [  '1+2+3+4 5',     '3+4',                 '1 2 3+4 5',    4          ],
            [  '1 2 3 4 5',     '1+2 2+3 2+4 3+4 4+5', '1+2+3+4+5',    1, 3, 5    ],
            [  '1 2 3 4 5',     '1+2 1+3 1+4 1+5 2+3 ' .
                                '2+4 2+5 3+4 3+5 4+5', '1+2+3+4+5',    1, 3       ],
        ];
    }

    private function groupFacesByCloudStr(string $cloudStr, array $faces): array
    {
        $faceGroups = array_map(
            static fn($group) => array_filter(explode('+', $group)),
            array_filter(explode(' ', $cloudStr))
        );

        array_walk_recursive($faceGroups, static fn(&$v) => $v = $faces[$v - 1]);
        return $faceGroups;
    }

    private function enqueueSimilarIdsAndRunTask(UserDocumentFace ...$faces): void
    {
        $dumpQuery = (new Query($this->db()))
            ->select(['face_id' => 'id', 'similar_id'])
            ->from(UserDocumentFaces::TABLE_NAME)
            ->orderBy(['id' => SORT_ASC]);

        self::consoleOutputToDebug(function () use ($dumpQuery) {
            Console::write("BEFORE:\n");
            Console::write(UserDocumentFaceSimilarities::TABLE_NAME . ":");
            ConsoleTable::dump((new Query($this->db()))->select(['face_a_id', 'face_b_id'])->from(UserDocumentFaceSimilarities::TABLE_NAME)->all());
            Console::write(UserDocumentFaces::TABLE_NAME . ":");
            ConsoleTable::dump($dumpQuery->all());
        });

        foreach ($faces as $face) {
            if (!isset($face->similar_id)) {
                self::fail('Invalid config queuedFaces must have similar_id');
            }
        }

        $this->repo(UserDocumentFaceSimilarQueues::class)->batchUpsert(array_map(static fn(UserDocumentFace $face) => [
            'similar_id' => $face->similar_id,
            'upserted_at' => new \DateTimeImmutable('-3 days'),
        ], $faces));

        $this->runTask('update-users-similar-faces-recursive', Res::CID, '--mode=ignoreOptimisticLock,noSleep,debugCte');

        self::consoleOutputToDebug(static function () use ($dumpQuery) {
            Console::write("AFTER:\n" . UserDocumentFaces::TABLE_NAME);
            ConsoleTable::dump($dumpQuery->all());
        });
    }

    private function assertFaceClouds(string $cloudStr, array $faces): void
    {
        $idsToNum = array_combine(array_column($faces, 'id'), range(1, count($faces)));

        $faceCloudsActual = (new Query($this->db()))
            ->select(['similar_id', 'similar_id_is_min_face_id' => 'similar_id = MIN(id)', 'ids' => "string_agg(id::text, '+' order by id)"])
            ->from(UserDocumentFaces::TABLE_NAME)
            ->where(['id' => array_keys($idsToNum)])
            ->groupBy(['similar_id'])
            ->orderBy(['MIN(id)' => SORT_ASC])
            ->all();

        foreach ($faceCloudsActual as $cloud) {
            if (!$cloud['similar_id']) {
                self::fail("Cloud {$cloud['ids']} not complete");
            } elseif (!$cloud['similar_id_is_min_face_id']) {
                self::fail("For cloud {$cloud['ids']} similar_id:{$cloud['similar_id']} is not min face_id");
            }
        }

        $faceCloudActual = array_map(static fn($v) => explode('+', $v), array_column($faceCloudsActual, 'ids'));
        array_walk_recursive($faceCloudActual, static fn(&$id) => $id = $idsToNum[$id]);
        $faceCloudActual = implode(' ', array_map(static fn($g) => implode('+', $g), $faceCloudActual));

        self::assertSame($cloudStr, $faceCloudActual, 'Face clouds mismatch');
    }

    private function setSimilarIdPerGroup(array ...$faceGroups): void
    {
        $repo = $this->repo(UserDocumentFaces::class);
        foreach ($faceGroups as $faces) {
            $similarId = min(array_map(static fn(UserDocumentFace $f) => $f->id, $faces));
            foreach ($faces as $face) {
                /** @var UserDocumentFace $face */
                $face->similar_id = $similarId;
                $repo->update($face, ['similar_id']);
            }
        }
    }

    private function insertSimilarityPerGroup(array ...$faceGroups): void
    {
        if (empty($faceGroups)) {
            return;
        }

        $newFaceSims = [];
        foreach ($faceGroups as $faces) {
            $faceIds = array_map(static fn(UserDocumentFace $f) => $f->id, $faces);
            for ($i = 1, $iMax = count($faceIds); $i < $iMax; $i++) {
                $newFaceSims[] = [
                    ...(array_combine(['face_a_id', 'face_b_id'], [max($faceIds[$i], $faceIds[$i - 1]), min($faceIds[$i], $faceIds[$i - 1])])),
                    'distance' => 0,
                    'approved' => true,
                ];
            }
        }

        $this->db()->createCommand()
            ->batchInsert(UserDocumentFaceSimilarities::TABLE_NAME, array_keys($newFaceSims[0]), $newFaceSims)
            ->execute();
    }

    private function haveFaces(int $count): array
    {
        return $this->haveRecords(UserDocumentFaces::class, array_map(fn() => [
            'user_document_id' => $this->haveDoc()->id,
            'ratio' => 1,
            'box' => new PgArray([1,2,3,4]),
            'is_valid' => true,
        ], range(0, $count - 1)));
    }

    private function haveDoc(): UserDocument
    {
        $user = $this->haveUserRecord();
        $filename = substr(md5((string)self::uniqRuntimeId()), 0, 17);
        $doc = $this->haveRecord(UserDocuments::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'filename' => $filename,
            'original_name' => $filename,
            'tags' => new PgArray(['fake'], PgArray::TYPE_VARCHAR),
        ]);
        self::assertInstanceOf(UserDocument::class, $doc);
        return $doc;
    }
}
