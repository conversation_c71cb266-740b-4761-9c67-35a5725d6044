<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\modules\task\actions\update\RefcodesWebmasterTask;
use app\back\repositories\Refcodes;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(RefcodesWebmasterTask::class)]
class RefcodesWebmasterTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testUpdateWebmasterId(): void
    {
        $this->haveRates();

        $haveCodes = [
            1 => 'vp_w40817c136605l13882gplp1225_B10s1vozpPOcwarY-B3BYiF0CEig8',
            2 => 'aff_023fd5_34_GooglePlay.30.11',
            3 => 'vip104562_cstr-43007-48dac406',
            4 => 'mb_BQBxJwAAPGQAAEAfAAA.2023-04.24.22149',
        ];

        $expectedWebmasterIds = [
            1 => '40817',
            2 => '023fd5',
            3 => '104562',
            4 => null,
        ];

        foreach ($haveCodes as $id => $code) {
            $this->haveRefcodeRecord(['id' => $id, 'code' => $code]);
        }

        $this->runTask('update-refcodes-webmaster', Res::DEFAULT);

        foreach (array_combine($haveCodes, $expectedWebmasterIds) as $code => $webmasterId) {
            $this->seeRecordWithFields(
                Refcodes::class,
                ['code' => $code],
                ['webmaster_id' => $webmasterId]
            );
        }
    }
}
