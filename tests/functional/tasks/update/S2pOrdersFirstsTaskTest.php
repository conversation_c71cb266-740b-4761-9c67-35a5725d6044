<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\entities\S2pOrder;
use app\back\entities\Site;
use app\back\modules\task\actions\update\S2pOrdersFirstsTask;
use app\back\repositories\S2pOrders;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(S2pOrdersFirstsTask::class)]
class S2pOrdersFirstsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    public function testFistPayment(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $this->haveRates();

        $firstTryOrder = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_FAIL,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable('-30 sec'),
            'date_created' => new \DateTimeImmutable('-30 sec'),
        ]);
        $firstSuccessOrder = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable('-20 sec'),
            'date_created' => new \DateTimeImmutable('-20 sec'),
        ]);
        $secondSuccessOrder = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable('-10 sec'),
            'date_created' => new \DateTimeImmutable('-11 sec'),
        ]);

        $startTime = date('Y-m-d H:i:s', strtotime('-1 day'));

        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pOrders::class, [
            'id' => $firstTryOrder->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $firstSuccessOrder->id,
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $secondSuccessOrder->id,
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        // simulate s2p-orders task reload
        $firstTryOrder->is_first_success = false;
        $firstTryOrder->is_first_try = false;
        $this->repo(S2pOrders::class)->update($firstTryOrder, ['is_first_success', 'is_first_try']);
        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');
        $this->seeRecord(S2pOrders::class, [
            'id' => $firstTryOrder->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);

        $datetime = new \DateTimeImmutable('-1 minute');
        $thirdSuccessOrderWithEarlyDate = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => $datetime,
            'date_created' => new \DateTimeImmutable('-1 minute'),
        ]);
        // simulate variable microseconds round direction for run update-s2p-orders-firsts after reload s2p-orders
        $this->container()->get(S2pOrders::class)->batchUpsert([[
            'id' => $thirdSuccessOrderWithEarlyDate->id,
            'date' => $datetime->format('Y-m-d\TH:i:s.u'),
        ]]);

        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pOrders::class, [
            'id' => $firstSuccessOrder->id,
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $thirdSuccessOrderWithEarlyDate->id,
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        // check is_first_try later than new successful order
        $this->seeRecord(S2pOrders::class, [
            'id' => $firstTryOrder->id,
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        // simulate s2p-orders task reload
        $thirdSuccessOrderWithEarlyDate->is_first_success = false;
        $thirdSuccessOrderWithEarlyDate->is_first_try = false;
        $this->repo(S2pOrders::class)->update($thirdSuccessOrderWithEarlyDate, ['is_first_success', 'is_first_try']);
        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');
        $this->seeRecord(S2pOrders::class, [
            'id' => $thirdSuccessOrderWithEarlyDate->id,
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        // receive fail after success
        $thirdSuccessOrderWithEarlyDate->status = S2pOrder::STATUS_FAIL;
        $this->repo(S2pOrders::class)->update($thirdSuccessOrderWithEarlyDate, ['status']);
        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');
        $this->seeRecord(S2pOrders::class, [
            'id' => $thirdSuccessOrderWithEarlyDate->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);
        $this->seeRecord(S2pOrders::class, [
            'id' => $firstSuccessOrder->id,
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $successOrderNext = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable('-5 sec'),
            'date_created' => new \DateTimeImmutable('-6 sec'),
        ]);

        $startTimeToday = date('Y-m-d H:i:s', strtotime('today'));
        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTimeToday, 'P1D');

        $this->seeRecord(S2pOrders::class, [
            'id' => $successOrderNext->id,
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $userId2 = self::uniqRuntimeId();
        $firstOrderUser2 = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_NEW,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId2,
            'date' => new \DateTimeImmutable('-1 minute'),
            'date_created' => new \DateTimeImmutable('-2 minute'),
        ]);

        $secondOrderUser2 = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_IN_PROCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId2,
            'date' => new \DateTimeImmutable('-20 sec'),
            'date_created' => new \DateTimeImmutable('-20 sec'),
        ]);

        $this->runTask('update-s2p-orders-firsts', 'S2P', $startTime, 'P1D');

        $this->seeRecord(S2pOrders::class, [
            'id' => $firstOrderUser2->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $secondOrderUser2->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);
    }


    public function testLaterUpdatedDate(): void
    {
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();

        $d = '2024-01-18';
        $this->haveRates();

        $order1 = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable("$d 03:06:58"),
            'date_created' => new \DateTimeImmutable("$d 02:21:12"),
        ]);

        $order2 = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_FAIL,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable("$d 03:13:58"),
            'date_created' => new \DateTimeImmutable("$d 02:27:25"),
        ]);

        $order3 = $this->haveS2pOrderRecord([
            'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'site_id' => $siteId,
            'user_id' => $userId,
            'date' => new \DateTimeImmutable("$d 02:30:57"),
            'date_created' => new \DateTimeImmutable("$d 02:30:15"),
        ]);

        $this->runTask('update-s2p-orders-firsts', 'S2P', $d, 'P1D');

        $this->seeRecord(S2pOrders::class, [
            'id' => $order1->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $order2->id,
            'is_first_success' => false,
            'is_first_try' => true,
        ]);

        $this->seeRecord(S2pOrders::class, [
            'id' => $order3->id,
            'is_first_success' => true,
            'is_first_try' => true,
        ]);
    }
}
