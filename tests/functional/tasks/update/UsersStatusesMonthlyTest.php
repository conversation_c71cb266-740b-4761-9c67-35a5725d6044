<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\Country;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserStatusVipThreshold;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersStatusesMonthlyTask;
use app\back\repositories\Users;
use app\back\repositories\UserStatusVipThresholds;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;

#[CoversClass(UsersStatusesMonthlyTask::class)]
class UsersStatusesMonthlyTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public static function vipPromotionRuleProvider(): array
    {
        return [
            'generic rule' => [
                'thresholdSiteId' => UserStatusVipThreshold::SITE_ID_ANY,
            ],
            'specific rule' => [
                'thresholdSiteId' => Site::CV,
            ],
        ];
    }

    public static function countrySpecificThresholdProvider(): array
    {
        return [
            'country-specific threshold takes precedence over generic (promotes)' => [
                'userCountry' => Country::DE,
                'specificThresholdCountry' => Country::DE,
                'specificThresholdAmount' => 500,
                'genericThresholdAmount' => 900,
                'transactionAmount' => 600,
                'shouldPromote' => true,
            ],
            'falls back to generic threshold when no country-specific exists' => [
                'userCountry' => Country::DE,
                'specificThresholdCountry' => Country::PL,
                'specificThresholdAmount' => 500,
                'genericThresholdAmount' => 900,
                'transactionAmount' => 600,
                'shouldPromote' => false,
            ],
            'country-specific threshold blocks promotion when generic would allow' => [
                'userCountry' => Country::PL,
                'specificThresholdCountry' => Country::PL,
                'specificThresholdAmount' => 800,
                'genericThresholdAmount' => 500,
                'transactionAmount' => 600,
                'shouldPromote' => false,
            ],
        ];
    }

    public static function thresholdSettingsProvider(): array
    {
        return [
            'is_up=false blocks promotion' => [false, 'P30D', 'P5D', false],
            'is_up=true with recent transaction promotes' => [true, 'P30D', 'P5D', true],
            'is_up=true with old transaction blocks' => [true, 'P7D', 'P10D', false],
        ];
    }

    public static function userDowngradeProvider(): array
    {
        return [
            'ASP to VIP downgrade with low activity' => [
                'initialStatus' => User::STATUS_ASP,
                'targetStatus' => User::STATUS_VIP,
                'statusUpdatedAge' => 'P40D', // 40 days ago
                'transactionAmount' => 100,
                'thresholdAmount' => 1000,
                'downMultiplier' => 2,
                'isDown' => true,
                'periodDown' => 'P30D',
                'shouldDowngrade' => true,
            ],
            'disabled downgrade rule (is_down=false)' => [
                'initialStatus' => User::STATUS_ASP,
                'targetStatus' => User::STATUS_VIP,
                'statusUpdatedAge' => 'P40D',
                'transactionAmount' => 100,
                'thresholdAmount' => 1000,
                'downMultiplier' => 2,
                'isDown' => false,
                'periodDown' => 'P30D',
                'shouldDowngrade' => false,
            ],
            'recent status update prevents downgrade' => [
                'initialStatus' => User::STATUS_ASP,
                'targetStatus' => User::STATUS_VIP,
                'statusUpdatedAge' => 'P5D', // Too recent
                'transactionAmount' => 100,
                'thresholdAmount' => 1000,
                'downMultiplier' => 2,
                'isDown' => true,
                'periodDown' => 'P30D',
                'shouldDowngrade' => false,
            ],
        ];
    }

    #[DataProvider('vipPromotionRuleProvider')]
    public function testNormalActiveToVipPromotion(int $thresholdSiteId): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user with normal/active status
        $user = $this->createTestUser($siteUser, $monthBegin);

        // Add initial transaction below threshold
        $this->addUserTransaction($siteUser, $monthBegin, 800, 'P1D');

        // Run task - should not affect user (no threshold yet)
        $this->runTask('update-users-statuses-monthly', $res);
        $this->assertUserStatusUnchanged($siteUser, $user);

        // Add threshold rule
        $this->createVipThreshold($thresholdSiteId);

        // Run task - should not affect user (amount still below threshold)
        $this->runTask('update-users-statuses-monthly', $res);
        $this->assertUserStatusUnchanged($siteUser, $user);

        // Add another transaction to exceed threshold
        $this->addUserTransaction($siteUser, $monthBegin, 200, 'P20D');

        // Run task - should promote user to VIP
        $this->runTask('update-users-statuses-monthly', $res);
        $this->assertUserPromotedToVip($siteUser, $user);
    }

    #[DataProvider('countrySpecificThresholdProvider')]
    public function testCountrySpecificThresholdPrecedence(
        string $userCountry,
        string $specificThresholdCountry,
        int $specificThresholdAmount,
        int $genericThresholdAmount,
        int $transactionAmount,
        bool $shouldPromote
    ): void {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        // Create user with specific country
        $user = $this->createTestUser($siteUser, $monthBegin, $userCountry);

        // Create country-specific threshold
        $this->createVipThreshold(country: $specificThresholdCountry, amount: $specificThresholdAmount);

        // Create generic threshold (should be ignored if country-specific exists for user's country)
        $this->createVipThreshold(amount: $genericThresholdAmount);

        // Add transaction
        $this->addUserTransaction($siteUser, $monthBegin, $transactionAmount, 'P1D');

        // Run task
        $this->runTask('update-users-statuses-monthly', $res);

        // Assert result based on expected behavior
        if ($shouldPromote) {
            $this->assertUserPromotedToVip($siteUser, $user);
        } else {
            $this->assertUserStatusUnchanged($siteUser, $user);
        }
    }

    #[DataProvider('thresholdSettingsProvider')]
    public function testThresholdSettings(bool $isUp, string $periodUp, string $transactionAge, bool $shouldPromote): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $res = Res::getResourceNameBySiteId($siteId);
        $siteUser = ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId()];
        $monthBegin = new \DateTimeImmutable(date('Y-m-01'));

        $user = $this->createTestUser($siteUser, $monthBegin);
        $this->addUserTransaction($siteUser, $monthBegin, 1000, $transactionAge);
        $this->createVipThreshold(amount: 500, isUp: $isUp, periodUp: $periodUp);
        $this->runTask('update-users-statuses-monthly', $res);

        $shouldPromote ? $this->assertUserPromotedToVip($siteUser, $user) : $this->assertUserStatusUnchanged($siteUser, $user);
    }

    private function createTestUser(array $siteUser, \DateTimeImmutable $monthBegin, ?string $country = null): User
    {
        $userData = [
            ...$siteUser,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'status_updated_at' => $monthBegin->sub(new \DateInterval('P1M')),
        ];

        if ($country !== null) {
            $userData['country'] = $country;
        }

        return $this->haveUserRecord($userData);
    }

    private function addUserTransaction(array $siteUser, \DateTimeImmutable $monthBegin, int $amount, string $dayInterval): void
    {
        $this->haveUserTransactionRecord([
            ...$siteUser,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'updated_at' => $monthBegin->sub(new \DateInterval($dayInterval)),
            'currency' => Rate::EUR,
            'amount_orig' => $amount,
        ]);
    }

    private function createVipThreshold(
        int $siteId = UserStatusVipThreshold::SITE_ID_ANY,
        string $country = Country::DEFAULT,
        int $amount = 900,
        bool $isUp = true,
        string $periodUp = 'P30D'
    ): void {
        $this->haveRecord(UserStatusVipThresholds::class, [
            'country' => $country,
            'site_id' => $siteId,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'amount' => $amount,
            'is_up' => $isUp,
            'period_up' => new \DateInterval($periodUp),
            'check_period' => UserStatusVipThreshold::CHECK_PERIOD_MONTH,
            'is_down' => false,
            'period_down' => new \DateInterval('P1D'),
        ]);
    }

    private function assertUserStatusUnchanged(array $siteUser, User $originalUser): void
    {
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $originalUser->status,
            'active_status' => $originalUser->active_status,
            'status_updated_at' => $originalUser->status_updated_at,
        ]);
    }

    private function assertUserPromotedToVip(array $siteUser, User $originalUser): void
    {
        /** @var User $actualUser */
        $actualUser = $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
        ]);

        static::assertGreaterThan($originalUser->status_updated_at, $actualUser->status_updated_at);
    }
}
