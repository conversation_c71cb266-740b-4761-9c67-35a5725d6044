<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\Country;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserStatusVipThreshold;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersStatusesMonthlyTask;
use app\back\repositories\Users;
use app\back\repositories\UserStatusVipThresholds;
use app\back\repositories\UserTransactions;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use Yiisoft\Db\Query\Query;

#[CoversClass(UsersStatusesMonthlyTask::class)]
class UsersStatusesMonthlyTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testNormalActiveToVipPromotionGenericRule(): void
    {
        $this->haveRates();
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();
        $res = Res::getResourceNameBySiteId($siteId);

        $siteUser = ['site_id' => $siteId, 'user_id' => $userId];

        $monthBegin = new \DateTimeImmutable('first day of currenct month');

        print_r($monthBegin->format('Y-m-d H:i:s'));
die();
        $user = $this->haveUserRecord([
            ...$siteUser,
            'status' => User::STATUS_NORMAL,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'status_updated_at' => $monthBegin->sub(new \DateInterval('P1M')),
        ]);

        $this->haveUserTransactionRecord([
            ...$siteUser,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'updated_at' => $monthBegin->sub(new \DateInterval('P1D')),
            'currency' => Rate::EUR,
            'amount_orig' => 800,
        ]);

        $this->runTask('update-users-statuses-monthly', $res);

        // Not affected, because no threshold
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $user->status,
            'active_status' => $user->active_status,
            'status_updated_at' => $user->status_updated_at,
        ]);

        $this->haveRecord(UserStatusVipThresholds::class, [
            'country' => Country::DEFAULT,
            'site_id' => UserStatusVipThreshold::SITE_ID_ANY,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'amount' => 900,
            'is_up' => true,
            'period_up' => new \DateInterval('P30D'),
            'check_period' => UserStatusVipThreshold::CHECK_PERIOD_MONTH,
            'is_down' => false,
            'period_down' => new \DateInterval('P1D'),
        ]);

        $this->runTask('update-users-statuses-monthly', $res);

        // Not affected, because threshold is bigger
        $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => $user->status,
            'active_status' => $user->active_status,
            'status_updated_at' => $user->status_updated_at,
        ]);

        $this->haveUserTransactionRecord([
            ...$siteUser,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'updated_at' => $monthBegin->sub(new \DateInterval('P20D')),
            'currency' => Rate::EUR,
            'amount_orig' => 200,
        ]);

        $allTransactions = (new Query($this->db()))
            ->from(UserTransactions::TABLE_NAME)
            ->where($siteUser)
            ->all();

        print_r($allTransactions);

        $this->runTask('update-users-statuses-monthly', $res);

        /** @var User $actualUser */
        $actualUser = $this->seeRecordWithFields(Users::class, $siteUser, [
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
        ]);

        static::assertGreaterThan($user->status_updated_at, $actualUser->status_updated_at);
    }
}
