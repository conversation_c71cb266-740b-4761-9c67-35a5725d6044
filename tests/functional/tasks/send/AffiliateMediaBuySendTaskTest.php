<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\send;

use app\back\entities\enums\WpAffOwner;
use app\back\entities\enums\WpLocation;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\TrafficSource;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\repositories\UserSpecialInfos;
use app\tests\functional\tasks\BaseSendTaskTestCase;
use app\tests\libs\DbTransactionalUnitTrait;

class AffiliateMediaBuySendTaskTest extends BaseSendTaskTestCase
{
    use DbTransactionalUnitTrait;

    private const string WP_AFF_OWNER_VP = 'V.P';
    private const string WP_AFF_OWNER_WP = 'WP';
    private const string WP_AFF_OWNER_VB = 'VB';
    private const string WP_AFF_OWNER_GG = 'GG';

    private const string WP_LOCATION_WP = 'WelcomePartners';
    private const string WP_LOCATION_VP = 'V.Partners';
    private const string WP_LOCATION_VB = 'VBaffs';
    private const string WP_LOCATION_GG = 'GG';

    private const array AFF_OWNERS = [
        self::WP_AFF_OWNER_VP,
        self::WP_AFF_OWNER_WP,
        self::WP_AFF_OWNER_VB,
        self::WP_AFF_OWNER_GG,
    ];

    private const array WP_LOCATIONS = [
        self::WP_LOCATION_WP,
        self::WP_LOCATION_VP,
        self::WP_LOCATION_VB,
        self::WP_LOCATION_GG,
    ];

    protected string $contactName = 'affiliate_media_buy_report_emails';

    public static function setUpBeforeClass(): void
    {
        foreach (self::AFF_OWNERS as $ao) {
            WpAffOwner::check($ao);
        }

        foreach (self::WP_LOCATIONS as $l) {
            WpLocation::check($l);
        }
    }

    protected function setUp(): void
    {
        $this->haveContactRecord(['name' => $this->contactName]);
    }

    public function testData(): void
    {
        $this->haveRates();
        $affAffOwner1 = $this->haveWpAffOwnerGroupRecord(['name' => self::WP_AFF_OWNER_VP, 'group' => WpAffOwner::GROUP_VP]);
        $affAffOwner2 = $this->haveWpAffOwnerGroupRecord(['name' => self::WP_AFF_OWNER_WP, 'group' => WpAffOwner::GROUP_WP]);
        $affAffOwner3 = $this->haveWpAffOwnerGroupRecord(['name' => self::WP_AFF_OWNER_VB, 'group' => WpAffOwner::GROUP_VB]);

        $webmaster1 = $this->haveWpWebmasterRecord(['aff_owner' => $affAffOwner1->name, 'location' => self::WP_LOCATION_VP]);
        $webmaster2 = $this->haveWpWebmasterRecord(['aff_owner' => $affAffOwner2->name, 'location' => self::WP_LOCATION_WP]);
        $webmaster3 = $this->haveWpWebmasterRecord(['aff_owner' => $affAffOwner3->name, 'location' => self::WP_LOCATION_VB]);

        $refcode1 = $this->haveRefcodeRecord(['ts_id' => TrafficSource::TS_MARKETING, 'webmaster_id' => (string)self::uniqRuntimeId()]);
        $refcode2 = $this->haveRefcodeRecord(['ts_id' => TrafficSource::TS_AFFILIATES, 'webmaster_id' => (string)$webmaster1->id]);
        $refcode3 = $this->haveRefcodeRecord(['ts_id' => TrafficSource::TS_AFFILIATES, 'webmaster_id' => (string)$webmaster2->id]);
        $refcode4 = $this->haveRefcodeRecord(['ts_id' => TrafficSource::TS_AFFILIATES, 'webmaster_id' => (string)$webmaster3->id]);

        $user1 = $this->haveUserRecord(['site_id' => Site::CV, 'refcode_id' => $refcode1->id]);
        $user2 = $this->haveUserRecord(['site_id' => Site::GMSD, 'refcode_id' => $refcode2->id]);
        $user3 = $this->haveUserRecord(['site_id' => Site::GMSD, 'refcode_id' => $refcode3->id]);
        $user4 = $this->haveUserRecord(['site_id' => Site::GMSD, 'refcode_id' => $refcode4->id]);

        $this->haveUserStatCustom($user1, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => true, 'amount_orig' => '250']);
        $this->haveUserStatCustom($user1, ['op_id' => UserTransaction::OP_OUT, 'is_first_success' => false, 'amount_orig' => '250']);
        $this->haveUserStatCustom($user2, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => false, 'amount_orig' => '200']);
        $this->haveUserStatCustom($user2, ['op_id' => UserTransaction::OP_OUT, 'is_first_success' => false, 'amount_orig' => '300']);
        $this->haveUserStatCustom($user2, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => true, 'amount_orig' => '250']);
        $this->haveUserStatCustom($user3, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => true, 'amount_orig' => '150']);
        $this->haveUserStatCustom($user4, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => true, 'amount_orig' => '100']);

        $this->runTask('send-affiliate-media-buy', 'SEND', '2020-01-30');
        $messages = $this->getTestMessages();

        self::assertCount(1, $messages);

        $expectedDataSet = [
            ['content' => $this->renderEmailTable([
                'data' => [
                    [
                        'trafficSource' => 'Affiliates',
                        'fdCount' => '1',
                        'fdSum' => '150',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '150',
                        'wdSum' => '0',
                        'ngr' => '150',
                    ],
                    [
                        'trafficSource' => 'Marketing',
                        'fdCount' => '1',
                        'fdSum' => '250',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '250',
                        'wdSum' => '250',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'WP vs MB',
                        'fdCount' => '0%',
                        'fdSum' => '-40%',
                        'rdCount' => '0%',
                        'rdSum' => '0%',
                        'dSum' => '-40%',
                        'wdSum' => '-100%',
                        'ngr' => '0%',
                    ],
                ],
                'cols' => [
                    'trafficSource' => 'Traffic source',
                    'fdCount' => 'F count',
                    'fdSum' => 'F sum',
                    'rdCount' => 'R count',
                    'rdSum' => 'R sum',
                    'dSum' => 'F+R sum',
                    'wdSum' => 'W sum',
                    'ngr' => 'NGR',
                ],
                'title' => 'WP vs MB',
            ])],
            ['content' => $this->renderEmailTable([
                'data' => [
                    [
                        'trafficSource' => 'Affiliates',
                        'fdCount' => '2',
                        'fdSum' => '350',
                        'rdCount' => '1',
                        'rdSum' => '200',
                        'dSum' => '550',
                        'wdSum' => '300',
                        'ngr' => '250',
                    ],
                    [
                        'trafficSource' => 'Marketing',
                        'fdCount' => '1',
                        'fdSum' => '250',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '250',
                        'wdSum' => '250',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'VP+VB vs MB',
                        'fdCount' => '100%',
                        'fdSum' => '40%',
                        'rdCount' => '0%',
                        'rdSum' => '0%',
                        'dSum' => '120%',
                        'wdSum' => '20%',
                        'ngr' => '0%',
                    ],
                ],
                'cols' => [
                    'trafficSource' => 'Traffic source',
                    'fdCount' => 'F count',
                    'fdSum' => 'F sum',
                    'rdCount' => 'R count',
                    'rdSum' => 'R sum',
                    'dSum' => 'F+R sum',
                    'wdSum' => 'W sum',
                    'ngr' => 'NGR',
                ],
                'title' => 'VP+VB vs MB',
            ])],
        ];

        $this->prepareAndCompare($expectedDataSet, $messages[0]->getHtmlBody(), 'data');

        $affAffOwnerNotCompare = $this->haveWpAffOwnerGroupRecord(['name' => self::WP_AFF_OWNER_GG, 'group' => WpAffOwner::GROUP_GG]);
        $webmasterNotCompare = $this->haveWpWebmasterRecord(['aff_owner' => $affAffOwnerNotCompare->name, 'location' => self::WP_LOCATION_GG]);
        $refcodeNotCompare = $this->haveRefcodeRecord(['ts_id' => TrafficSource::TS_AFFILIATES, 'webmaster_id' => (string)$webmasterNotCompare->id]);
        $userNotCompare = $this->haveUserRecord(['site_id' => Site::GMSD, 'refcode_id' => $refcodeNotCompare->id]);
        $this->haveUserStatCustom($userNotCompare, ['op_id' => UserTransaction::OP_IN, 'is_first_success' => true, 'amount_orig' => '100']);

        $this->runTask('send-affiliate-media-buy', 'SEND', '2020-01-30');
        $messagesNew = $this->getTestMessages();
        self::assertCount(1, $messagesNew);

        //not changed
        $this->prepareAndCompare($expectedDataSet, $messagesNew[0]->getHtmlBody(), 'data');
    }

    public function testNoData(): void
    {
        $this->runTask('send-affiliate-media-buy', 'SEND', '2020-01-30');

        $messages = $this->getTestMessages();

        $expectedDataSet = [
            ['content' => $this->renderEmailTable([
                'data' => [
                    [
                        'trafficSource' => 'Affiliates',
                        'fdCount' => '0',
                        'fdSum' => '0',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '0',
                        'wdSum' => '0',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'Marketing',
                        'fdCount' => '0',
                        'fdSum' => '0',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '0',
                        'wdSum' => '0',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'WP vs MB',
                        'fdCount' => '0%',
                        'fdSum' => '0%',
                        'rdCount' => '0%',
                        'rdSum' => '0%',
                        'dSum' => '0%',
                        'wdSum' => '0%',
                        'ngr' => '0%',
                    ],
                ],
                'cols' => [
                    'trafficSource' => 'Traffic source',
                    'fdCount' => 'F count',
                    'fdSum' => 'F sum',
                    'rdCount' => 'R count',
                    'rdSum' => 'R sum',
                    'dSum' => 'F+R sum',
                    'wdSum' => 'W sum',
                    'ngr' => 'NGR',
                ],
                'title' => 'WP vs MB',
                ])
            ],
            ['content' => $this->renderEmailTable([
                'data' => [
                    [
                        'trafficSource' => 'Affiliates',
                        'fdCount' => '0',
                        'fdSum' => '0',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '0',
                        'wdSum' => '0',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'Marketing',
                        'fdCount' => '0',
                        'fdSum' => '0',
                        'rdCount' => '0',
                        'rdSum' => '0',
                        'dSum' => '0',
                        'wdSum' => '0',
                        'ngr' => '0',
                    ],
                    [
                        'trafficSource' => 'VP+VB vs MB',
                        'fdCount' => '0%',
                        'fdSum' => '0%',
                        'rdCount' => '0%',
                        'rdSum' => '0%',
                        'dSum' => '0%',
                        'wdSum' => '0%',
                        'ngr' => '0%',
                    ],
                ],
                'cols' => [
                    'trafficSource' => 'Traffic source',
                    'fdCount' => 'F count',
                    'fdSum' => 'F sum',
                    'rdCount' => 'R count',
                    'rdSum' => 'R sum',
                    'dSum' => 'F+R sum',
                    'wdSum' => 'W sum',
                    'ngr' => 'NGR',
                ],
                'title' => 'VP+VB vs MB',
                ])
            ],
        ];

        $this->prepareAndCompare($expectedDataSet, $messages[0]->getHtmlBody(), 'data');
    }

    private function haveUserStatCustom(User $user, array $attributes): void
    {
        $dateUpdate = (new \DateTimeImmutable('2020-01-14 00:00:00'))
            ->add(new \DateInterval('PT' . random_int(0, 24 * 3600) . 'S'));

        $this->haveUserTransactionRecord([
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'updated_at' => $dateUpdate,
            'currency' => Rate::USD,
            ...$attributes,
        ]);

        if (!empty($attributes['is_first_success'])) {
            $this->haveRecord(UserSpecialInfos::class, [
                'site_id' => $user->site_id,
                'user_id' => $user->user_id,
                'dep_first_at' => $dateUpdate,
            ]);
        }
    }
}
