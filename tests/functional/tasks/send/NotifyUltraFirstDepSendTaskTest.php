<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\send;

use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\send\NotifyUltraFirstDepTask;
use app\back\repositories\Sites;
use app\tests\functional\tasks\BaseSendTaskTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(NotifyUltraFirstDepTask::class)]
class NotifyUltraFirstDepSendTaskTest extends BaseSendTaskTestCase
{
    use DbTransactionalUnitTrait;

    public function testData(): void
    {
        $this->haveRates();

        $operator1 = $this->haveOperatorRecord();
        $operator2 = $this->haveOperatorRecord();
        $operator3 = $this->haveOperatorRecord();

        $sites = $this->repo(Sites::class);

        $user1 = $this->haveUserRecord(['personal_manager' => $operator1->id, 'status' => User::STATUS_VIP, 'site_id' => Site::GMS]);
        $user2 = $this->haveUserRecord(['personal_manager' => $operator2->id, 'status' => User::STATUS_ULTRA, 'site_id' => Site::CV]);
        $user3 = $this->haveUserRecord(['personal_manager' => $operator1->id, 'status' => User::STATUS_ULTRA, 'site_id' => Site::GGB]);
        $user4 = $this->haveUserRecord(['personal_manager' => $operator3->id, 'status' => User::STATUS_ULTRA, 'site_id' => Site::GGUA]);
        $user5 = $this->haveUserRecord(['personal_manager' => $operator2->id, 'status' => User::STATUS_ULTRA, 'site_id' => Site::GGUA]);

        $this->haveUserStatRecordCustom($user1, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '200']);
        $this->haveUserStatRecordCustom($user2, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '250']);
        $this->haveUserStatRecordCustom($user2, ['op_id' => UserTransaction::OP_OUT, 'amount_orig' => '250']);
        $this->haveUserStatRecordCustom($user3, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '300', 'amount_eur' => '10']);
        $this->haveUserStatRecordCustom($user3, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '300', 'amount_eur' => '12']);
        $this->haveUserStatRecordCustom($user4, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '350', 'updated_at' => new \DateTimeImmutable('2020-01-13')]);
        $this->haveUserStatRecordCustom($user4, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '400']);
        $this->haveUserStatRecordCustom($user5, ['op_id' => UserTransaction::OP_IN,  'amount_orig' => '200']);

        $this->runTask('send-notify-ultra-first-dep', 'SEND', '2020-01-14');

        $expected = [
            [
                'subject' => 'Ultra users first deposit (2020-01-14)',
                'to' => [$operator1->email],
                'content' => $this->renderEmailTable([
                    'title' => 'Ultra users first deposit (2020-01-14)',
                    'cols' => [
                        'site' => 'Site',
                        'user' => 'User',
                        'date' => 'Last dep date',
                        'sum' => 'Sum',
                        'currency' => 'Currency',
                    ],
                    'data' => [
                        [
                            'site_id' => $user3->site_id,
                            'user_id' => $user3->user_id,
                            'personal_manager' => $operator1->id,
                            'date' => '2020-01-14',
                            'sum' => '22.00',
                            'site' => $sites->getNameById($user3->site_id),
                            'user' => User::playerLink($user3->site_id, $user3->user_id),
                            'currency' => 'EUR',
                        ],
                    ],
                ]),
            ],
            [
                'subject' => 'Ultra users first deposit (2020-01-14)',
                'to' => [$operator2->email],
                'content' => $this->renderEmailTable([
                    'title' => 'Ultra users first deposit (2020-01-14)',
                    'cols' => [
                        'site' => 'Site',
                        'user' => 'User',
                        'date' => 'Last dep date',
                        'sum' => 'Sum',
                        'currency' => 'Currency',
                    ],
                    'data' => [
                        [
                            'site_id' => $user2->site_id,
                            'user_id' => $user2->user_id,
                            'personal_manager' => $operator2->id,
                            'date' => '2020-01-14',
                            'sum' => '250.00',
                            'site' => $sites->getNameById($user2->site_id),
                            'user' => User::playerLink($user2->site_id, $user2->user_id),
                            'currency' => 'RUB',
                        ],
                        [
                            'site_id' => $user5->site_id,
                            'user_id' => $user5->user_id,
                            'personal_manager' => $operator2->id,
                            'date' => '2020-01-14',
                            'sum' => '200.00',
                            'site' => $sites->getNameById($user5->site_id),
                            'user' => User::playerLink($user5->site_id, $user5->user_id),
                            'currency' => 'UAH',
                        ],
                    ],
                ]),
            ],
        ];
        $this->prepareAndCompare($expected, $this->getTestMessages(), 'NotifyUltraFirstDepAction');
    }

    private function haveUserStatRecordCustom(User $user, array $props): UserTransaction
    {
        return $this->haveUserTransactionRecord([
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'updated_at' => new \DateTimeImmutable('2020-01-14 +' . random_int(0, 24 * 3600 - 1) . 'seconds'),
            'currency' => Rate::RUB,
            ...$props,
        ]);
    }
}
