<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\send;

use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\send\NotifyVipInactiveDepositTask;
use app\back\repositories\Sites;
use app\back\repositories\UserSpecialInfos;
use app\tests\functional\tasks\BaseSendTaskTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(NotifyVipInactiveDepositTask::class)]
class NotifyVipInactiveDepositTaskTest extends BaseSendTaskTestCase
{
    use DbTransactionalUnitTrait;

    protected string $contactName = 'vip_inactive_deposit';

    private readonly Sites $sitesRepo;
    private int $siteId;
    private string $dateTest = '2023-02-02';

    protected function setUp(): void
    {
        parent::setUp();

        $this->sitesRepo = $this->container()->get(Sites::class);
    }

    public function testLastDepLessThreeDays(): void
    {
        $this->haveContactRecord(['name' => $this->contactName]);
        $this->haveRates();

        $user1 = $this->haveUserRecord(['status' => User::STATUS_VIP]);
        $user2 = $this->haveUserRecord(['status' => User::STATUS_ASP]);

        $this->haveSpecInfRecordCustom($user2, 'P1D');
        $this->haveSpecInfRecordCustom($user1, 'P2D');

        $this->runTask('send-notify-vip-inactive-deposit', 'SEND', $this->dateTest);

        $this->assertSame([], $this->getTestMessages());
    }

    public function testNotInEnumDays(): void
    {
        $this->haveContactRecord(['name' => $this->contactName]);
        $this->haveRates();

        $user1 = $this->haveUserRecord(['status' => User::STATUS_VIP]);
        $user2 = $this->haveUserRecord(['status' => User::STATUS_ASP]);
        $user3 = $this->haveUserRecord(['status' => User::STATUS_VIP]);
        $user4 = $this->haveUserRecord(['status' => User::STATUS_ASP]);

        $this->haveSpecInfRecordCustom($user1, 'P4D');
        $this->haveSpecInfRecordCustom($user2, 'P6D');
        $this->haveSpecInfRecordCustom($user3, 'P17D');
        $this->haveSpecInfRecordCustom($user4, 'P23D');

        $this->runTask('send-notify-vip-inactive-deposit', 'SEND', $this->dateTest);

        $this->assertSame([], $this->getTestMessages());
    }

    public function testInAllDays(): void
    {
        $siteId = Site::CV;

        $this->haveRates();

        $this->haveContactRecord(['name' => $this->contactName]);

        $statuses = [
            'status' => [User::STATUS_VIP, User::STATUS_ASP],
            'active_status' => [User::ACTIVE_STATUS_ACTIVE, User::ACTIVE_STATUS_NEW_IN, User::ACTIVE_STATUS_ACT_IN, User::ACTIVE_STATUS_RISK],
        ];

        for ($i = 1; $i <= 8; $i++) {
            $user = 'user' . $i;
            $statusId = random_int(0, 1);
            $activeStatusId = $statusId ? random_int(0, 1) : random_int(0, 3);
            $$user = $this->haveUserRecord([
                'site_id' => $siteId,
                'status' => $statuses['status'][$statusId],
                'active_status' => $statuses['active_status'][$activeStatusId],
                'cid' => self::uniqRuntimeId()]);
        }

        $this->haveSpecInfRecordCustom($user1, 'P3D');
        $this->haveSpecInfRecordCustom($user2, 'P5D');
        $this->haveSpecInfRecordCustom($user3, 'P7D');
        $this->haveSpecInfRecordCustom($user4, 'P14D');
        $this->haveSpecInfRecordCustom($user5, 'P21D');
        $this->haveSpecInfRecordCustom($user6, 'P21D');
        $this->haveSpecInfRecordCustom($user7, 'P7D');
        $this->haveSpecInfRecordCustom($user8, 'P3D');

        $this->haveUserStatRecordCustom($user1, '200.00');
        $this->haveUserStatRecordCustom($user2, '100.00');
        $this->haveUserStatRecordCustom($user2, '200.00');
        $this->haveUserStatRecordCustom($user3, '150.00');
        $this->haveUserStatRecordCustom($user3, '250.00');
        $this->haveUserStatRecordCustom($user3, '500.00');

        $this->haveUserStatRecordCustom($user4, '10000');
        $this->haveUserStatRecordCustom($user5, '20000');
        $this->haveUserStatRecordCustom($user6, '30000');
        $this->haveUserStatRecordCustom($user7, '40000');
        $this->haveUserStatRecordCustom($user8, '10');

        $this->haveUserStatRecordCustom($user6, '5000', true);
        $this->haveUserStatRecordCustom($user7, '5000', true);
        $this->haveUserStatRecordCustom($user8, '5000', true);

        $this->runTask('send-notify-vip-inactive-deposit', 'SEND', $this->dateTest);

        $expected = [
            'content' => $this->renderEmailTable([
                'title' => 'Inactive users AWOL (' . date('Y-m-d', strtotime($this->dateTest)) . ')',
                'cols' => [
                    'site' => 'Site',
                    'user_id' => 'User',
                    'status' => 'Player status',
                    'inactive_days' => 'Days without deps',
                    'avg_deposit' => 'Average deps, ' . Rate::RUB,
                ],
                'data' => [
                    $this->getExpectedRow($user5, days: 21, avg: '20000.00'),
                    $this->getExpectedRow($user6, days: 21, avg: '30000.00'),
                    $this->getExpectedRow($user4, days: 14, avg: '10000.00'),
                    $this->getExpectedRow($user3, days: 7, avg: '300.00'),
                    $this->getExpectedRow($user7, days: 7, avg: '40000.00'),
                    $this->getExpectedRow($user2, days: 5, avg: '150.00'),
                    $this->getExpectedRow($user1, days: 3, avg: '200.00'),
                    $this->getExpectedRow($user8, days: 3, avg: '10.00'),
                ],
            ]),
        ];
        $messages = $this->getTestMessages();
        $this->prepareAndCompare([$expected], $messages[0]->getHtmlBody(), __FUNCTION__);
    }


    public function testExclude(): void
    {
        $siteId1 = Site::GGB;
        $siteId2 = Site::VV;
        $siteId3 = Site::ICG;

        $this->haveRates();

        $this->haveContactRecord(['name' => $this->contactName]);

        $user1 = $this->haveUserRecord([
            'site_id' => $siteId1,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'cid' => self::uniqRuntimeId(),
        ]);

        $user2 = $this->haveUserRecord([
            'site_id' => $siteId2,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'cid' => self::uniqRuntimeId(),
            'locale' => 'ru'
        ]);

        $user3 = $this->haveUserRecord([
            'site_id' => $siteId3,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'cid' => self::uniqRuntimeId(),
            'locale' => 'ru'
        ]);

        $user4 = $this->haveUserRecord([
            'site_id' => $siteId3,
            'status' => User::STATUS_VIP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'cid' => self::uniqRuntimeId(),
            'locale' => 'uk'
        ]);

        $user5 = $this->haveUserRecord([
            'site_id' => $siteId2,
            'status' => User::STATUS_ASP,
            'active_status' => User::ACTIVE_STATUS_ACTIVE,
            'cid' => self::uniqRuntimeId(),
            'locale' => 'pt-br'
        ]);

        $this->haveSpecInfRecordCustom($user1, 'P3D');
        $this->haveSpecInfRecordCustom($user2, 'P3D');
        $this->haveSpecInfRecordCustom($user3, 'P3D');
        $this->haveSpecInfRecordCustom($user4, 'P3D');
        $this->haveSpecInfRecordCustom($user5, 'P5D');

        $this->haveUserStatRecordCustom($user1, '100.00');
        $this->haveUserStatRecordCustom($user2, '100.00');
        $this->haveUserStatRecordCustom($user3, '100.00');
        $this->haveUserStatRecordCustom($user4, '100.00');
        $this->haveUserStatRecordCustom($user5, '100.00');

        $this->runTask('send-notify-vip-inactive-deposit', 'SEND', $this->dateTest);

        $expected = [
            'content' => $this->renderEmailTable([
                'title' => 'Inactive users AWOL (' . date('Y-m-d', strtotime($this->dateTest)) . ')',
                'cols' => [
                    'site' => 'Site',
                    'user_id' => 'User',
                    'status' => 'Player status',
                    'inactive_days' => 'Days without deps',
                    'avg_deposit' => 'Average deps, ' . Rate::RUB,
                ],
                'data' => [
                    $this->getExpectedRow($user2, days: 3, avg: '100.00'),
                    $this->getExpectedRow($user3, days: 3, avg: '100.00'),
                ],
            ]),
        ];
        $messages = $this->getTestMessages();
        $this->prepareAndCompare([$expected], $messages[0]->getHtmlBody(), __FUNCTION__);
    }

    private function haveSpecInfRecordCustom(User $user, string $depLastAt): void
    {
        $fromDateTime = new \DateTimeImmutable($this->dateTest);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'dep_last_at' => $fromDateTime->sub(new \DateInterval($depLastAt)),
        ]);
    }

    private function getExpectedRow(User $user, int $days, string $avg): array
    {
        return [
            'site' => $this->sitesRepo->getShortNameById($user->site_id),
            'user_id' => User::playerLink($user->site_id, $user->user_id),
            'status' => User::getFullStatus((array)$user),
            'inactive_days' => $days,
            'avg_deposit' => $avg,
        ];
    }

    private function haveUserStatRecordCustom(User $user, string $amountRub, bool $dateOutOfRange = false): void
    {
        $fromDateTime = new \DateTimeImmutable($this->dateTest);
        $period = 'P' . ($dateOutOfRange ? random_int(60, 100) : random_int(21, 32)) . 'D';

        $this->haveUserTransactionRecord([
            'user_id' => $user->user_id,
            'site_id' => $user->site_id,
            'op_id' => UserTransaction::OP_IN,
            'amount_orig' => $amountRub,
            'currency' => Rate::RUB,
            'updated_at' => $fromDateTime->sub(new \DateInterval($period)),
            'status' => UserTransaction::STATUS_SUCCESS,
            'dir' => UserTransaction::DIR_IN,
        ]);
    }
}
