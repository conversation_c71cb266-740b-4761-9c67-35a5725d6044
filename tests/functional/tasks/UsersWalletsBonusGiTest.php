<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\entities\UserWallet;
use app\back\repositories\UserWallets;
use app\tests\libs\DbTransactionalUnitTrait;

class UsersWalletsBonusGiTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string DATA = <<<DATA
player_id,balance_id,amount,currency,updated_at,expired_at,deleted_at
14420087,9514089,100.000000000000,USD,2020-05-27T06:59:39.000+00:00,2020-06-09T13:13:17.000+00:00,
13545896,9464019,0.000000000000,USD,2020-05-27T06:59:34.000+00:00,2020-06-06T08:29:01.000+00:00,
14439146,9525417,10.260000000000,EUR,2020-05-27T06:52:40.000+00:00,2020-06-10T05:04:07.000+00:00,2020-05-27T06:52:40.000+00:00
13219772,9444033,0.000000000000,USD,2020-05-27T06:37:43.000+00:00,2020-06-05T02:38:04.000+00:00,2020-05-27T06:37:43.000+00:00
13219772,9448275,0.000000000000,USD,2020-05-27T06:37:43.000+00:00,2020-06-05T10:26:51.000+00:00,2020-05-27T06:37:43.000+00:00
DATA;

    public function testImport(): void
    {
        $userIds = [14420087, 13545896, 14439146, 13219772];

        foreach ($userIds as $userId) {
            $this->dontSeeRecord(UserWallets::class, ['site_id' => 29, 'user_id' => $userId]);
        }

        $this->haveRates();

        $this->runTask('users-wallets-bonus', 'GGB', $this->debugFile(static::DATA));

        $expectedRows = [
            9444033 => ['user_id' => 13219772, 'balance' => '0.00', 'balance_usd' => '0.00', 'balance_rub' => '0.00', 'balance_eur' => '0.00', 'currency' => 'USD', 'updated_at' => new \DateTimeImmutable('2020-05-27 06:37:43'), 'expired_at' => new \DateTimeImmutable('2020-06-05 02:38:04'), 'deleted_at' => new \DateTimeImmutable('2020-05-27 06:37:43')],
            9448275 => ['user_id' => 13219772, 'balance' => '0.00', 'balance_usd' => '0.00', 'balance_rub' => '0.00', 'balance_eur' => '0.00', 'currency' => 'USD', 'updated_at' => new \DateTimeImmutable('2020-05-27 06:37:43'), 'expired_at' => new \DateTimeImmutable('2020-06-05 10:26:51'), 'deleted_at' => new \DateTimeImmutable('2020-05-27 06:37:43')],
            9464019 => ['user_id' => 13545896, 'balance' => '0.00', 'balance_usd' => '0.00', 'balance_rub' => '0.00', 'balance_eur' => '0.00', 'currency' => 'USD', 'updated_at' => new \DateTimeImmutable('2020-05-27 06:59:34'), 'expired_at' => new \DateTimeImmutable('2020-06-06 08:29:01'), 'deleted_at' => null],
            9514089 => ['user_id' => 14420087, 'balance' => '100.00', 'balance_usd' => '100.00', 'balance_rub' => '7000.00', 'balance_eur' => '90.00', 'currency' => 'USD', 'updated_at' => new \DateTimeImmutable('2020-05-27 06:59:39'), 'expired_at' => new \DateTimeImmutable('2020-06-09 13:13:17'), 'deleted_at' => null],
            9525417 => ['user_id' => 14439146, 'balance' => '10.26', 'balance_usd' => '11.40', 'balance_rub' => '798.00', 'balance_eur' => '10.26', 'currency' => 'EUR', 'updated_at' => new \DateTimeImmutable('2020-05-27 06:52:40'), 'expired_at' => new \DateTimeImmutable('2020-06-10 05:04:07'), 'deleted_at' => new \DateTimeImmutable('2020-05-27 06:52:40')],
        ];

        foreach ($expectedRows as $walletId => $expectedRow) {
            $this->seeRecordWithFields(UserWallets::class, ['site_id' => 29, 'wallet_id' => $walletId, 'type' => UserWallet::TYPE_BONUS], $expectedRow);
        }
    }
}
