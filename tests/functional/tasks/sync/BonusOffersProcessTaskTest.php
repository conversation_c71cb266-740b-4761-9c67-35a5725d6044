<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\sync;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\UuidHelper;
use app\back\config\tasks\Res;
use app\back\entities\BonusOffer;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\sync\BonusOffersProcessTask;
use app\back\modules\task\requests\GinRequest;
use app\back\modules\task\TaskConfigs;
use app\back\modules\user\player\blocks\bonusOffers\BonusOfferCreateForm;
use app\back\repositories\BonusOffers;
use app\back\repositories\UserWallets;
use app\tests\libs\CmdUnitTrait;
use app\tests\libs\ContainerUnitTrait;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\ParametricUri;
use app\tests\libs\mock\PostRequest;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Nyholm\Psr7\Request;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

#[CoversClass(BonusOffersProcessTask::class)]
class BonusOffersProcessTaskTest extends TestCase
{
    use CmdUnitTrait;
    use ContainerUnitTrait;
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    private User $user;
    private string $currency;

    public function setUp(): void
    {
        $siteId = Site::VV;
        $this->currency  = Rate::EUR;
        $this->user = $this->haveUserRecord(['site_id' => $siteId]);

        $this->haveRecord(UserWallets::class, [
            'site_id' => $siteId, 'user_id' => $this->user->user_id, 'wallet_id' => self::uniqRuntimeId(), 'currency' => $this->currency,
        ]);
    }

    public function testSuccess(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $this->haveRates();
        $this->haveRates();

        $bonusOffer = $this->createBonusOffer($this->user->site_id, $this->user->user_id);

        $this->haveUserTransactionRecord([
            'site_id' => $this->user->site_id,
            'user_id' => $this->user->user_id,
            'op_id' => UserTransaction::OP_IN,
            'amount_orig' => '100.00',
            'currency' => $this->currency,
        ]);

        $bonusListRequest = new Request('GET', new ParametricUri('/api/brands/5/bonus-list', ['signature' => 'e3d1b5ce63801b1787e66a7d0a3ebe91', 'api_login' => 'analytics']));
        /** mock of GI bonuses config, most of real response not used anywhere */
        $bonusListResponse = new Response(200, [], new JsonBodyStream([
            'balance-action.create_bonus_balance_analytics' => [
                'schema' => [
                    'title' => 'legacy_analytics_action_request',
                    'type' => 'object',
                    'properties' => [
                        'balance_groups' => [
                            'type' => 'array',
                            'items' =>  [
                                'enum' => [
                                    'live-casino',
                                    'instant-games',
                                    'betting',
                                    'casino'
                                ],
                                'enum_titles' => [
                                    "live-casino",
                                    "instant-games",
                                    "betting",
                                    "casino"
                                ],
                            ],
                            'title' => 'balance_groups',
                        ],
                    ],
                ],
            ],
        ]));

        $bonusSendRequest = $this->vvSendBonusRequest(Res::VV, [
            'api_login' => 'analytics',
            'payload' => [
                'amount' => '30.00',
                'wager_multiplier' => 15,
                'time_to_live' => 'P5D',
                'balance_groups' => ['casino'],
                'currency_code' => $this->currency,
                'meta_data' => [
                    'anal/reason' => '6_BO_30_10_15',
                ],
            ],
            'idempotence_id' => UuidHelper::v5(UuidHelper::nil(), 'bonus_offer_' . $bonusOffer->id),
        ]);
        $bonusSendResponse = new Response(200, [], new JsonBodyStream([
            'success' => true,
        ]));

        MockServer::with(function () {
            $this->runTask('sync-bonus-offers-process', Res::VV);
        }, [
            [$bonusListRequest, $bonusListResponse],
            [$bonusSendRequest, $bonusSendResponse],
        ]);

        $this->seeRecordWithFields(BonusOffers::class, ['id' => $bonusOffer->id], ['status' => BonusOffer::STATUS_MET]);
    }

    private function vvSendBonusRequest(string $resource, array $params): Request
    {
        $url = "api/brands/5/players/{$this->user->user_id}/balance-action.create_bonus_balance_analytics";
        $ginAuthKey = $this->container()
            ->get(TaskConfigs::class)
            ->getTasksAccess()[$resource]['authKey'];

        $signature = GinRequest::urlAndSortedParamsHash($url, $ginAuthKey, $params);
        return new PostRequest(
            new ParametricUri("/$url", ['signature' => $signature]),
            new FormBodyStream($params),
        );
    }

    public function testDepLessThanRequired(): void
    {
        $this->haveRates();
        $bonusOffer = $this->createBonusOffer($this->user->site_id, $this->user->user_id);
        $this->container()->get(BonusOffers::class)->update($bonusOffer, ['created_at']);

        $this->haveUserTransactionRecord([
            'site_id' => $this->user->site_id,
            'user_id' => $this->user->user_id,
            'op_id' => UserTransaction::OP_IN,
            'amount_orig' => '5.00',
            'currency' => $this->currency,
        ]);

        $this->runTask('sync-bonus-offers-process', Res::VV);

        $this->seeRecordWithFields(BonusOffers::class, ['id' => $bonusOffer->id], ['status' => BonusOffer::STATUS_ACTIVE]);
    }

    public function testWithdrawal(): void
    {
        $this->haveRates();
        $bonusOffer = $this->createBonusOffer($this->user->site_id, $this->user->user_id);

        $this->haveUserTransactionRecord([
            'site_id' => $this->user->site_id,
            'user_id' => $this->user->user_id,
            'op_id' => UserTransaction::OP_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'amount_orig' => '100.00',
            'currency' => $this->currency,
        ]);

        $this->runTask('sync-bonus-offers-process', Res::VV);

        $this->seeRecordWithFields(BonusOffers::class, ['id' => $bonusOffer->id], ['status' => BonusOffer::STATUS_CANCELLED]);
    }

    public function testExpired(): void
    {
        $bonusOffer = $this->createBonusOffer($this->user->site_id, $this->user->user_id);
        $bonusOffer->created_at = $bonusOffer->created_at->sub(new \DateInterval('P1M'));
        $this->container()->get(BonusOffers::class)->update($bonusOffer, ['created_at']);

        $this->runTask('sync-bonus-offers-process', Res::VV);

        $this->seeRecordWithFields(BonusOffers::class, ['id' => $bonusOffer->id], ['status' => BonusOffer::STATUS_EXPIRED]);
    }

    private function createBonusOffer(int $siteId, int $userId): BonusOffer
    {
        $form = $this->container()->get(BonusOfferCreateForm::class);
        $form->validateOrException([
            'siteId' => $siteId,
            'userId' => $userId,
            'minDepSum' => 10,
            'maxPrize' => 1000,
            'activationTime' => 2,
            'prizePercent' => 30,
            'wager' => 15
        ]);

        $form->process();

        /** @var BonusOffer $bonusOffer */
        $bonusOffer = $this->seeRecordWithFields(BonusOffers::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
        ], [
            'status' => BonusOffer::STATUS_ACTIVE,
            'deposit_sum_min' => '10.00',
            'activation_time' => 2,
            'prize_percent' => 30,
            'remote_id' => null,
            'operator_id' => $this->container()->get(BaseAuthAccess::class)->employeeId(),
        ]);

        $bonusOffer->created_at = $bonusOffer->created_at->sub(new \DateInterval('PT1M'));
        $this->container()->get(BonusOffers::class)->update($bonusOffer, ['created_at']);

        return $bonusOffer;
    }
}
