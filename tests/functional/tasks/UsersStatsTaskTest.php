<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\UsersTransactionsTask;
use app\back\repositories\UserTransactions;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\TaskDebugFile;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersTransactionsTask::class)]
class UsersStatsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    public function testDontUpdateFirstSuccess(): void
    {
        $this->haveRates();
        $userId = self::uniqRuntimeId();
        $transactionId = (string) self::uniqRuntimeId();

        $this->runTask('users-stats', Res::CV, '-1 minute', 'PT1M', $this->debugCsvStat('2024-10-01 00:00:00', $userId, $transactionId));

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $this->runTask('update-users-transactions-firsts', Res::CV, '2024-10-01', 'PT1M');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $this->runTask('users-stats', Res::CV, '-1 minute', 'PT1M', $this->debugCsvStat('2024-10-01 05:00:00', $userId, $transactionId));
        $this->runTask('update-users-transactions-firsts', Res::CV, '2024-10-01', 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);
    }

    public function testUpdateFirstSuccessAfterTransactionFail(): void
    {
        $this->haveRates();
        $userId = self::uniqRuntimeId();
        $transactionId = (string) self::uniqRuntimeId();

        $this->runTask('users-stats', Res::CV, '-1 minute', 'PT1M', $this->debugCsvStat('2024-10-01 00:00:00', $userId, $transactionId));

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $this->runTask('update-users-transactions-firsts', Res::CV, '2024-10-01', 'PT1M');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $this->runTask('users-stats', Res::CV, '-1 minute', 'PT1M', $this->debugCsvStat('2024-10-01 05:00:00', $userId, $transactionId, UserTransaction::STATUS_FAIL));
        $this->runTask('update-users-transactions-firsts', Res::CV, '2024-10-01', 'P1D');

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => Site::CV,
            'user_id' => $userId
        ], [
            'is_first_success' => false,
            'is_first_try' => true,
        ]);
    }

    private function debugCsvStat(string $dateUpdate, int $userId, string $transactionId, int $status = UserTransaction::STATUS_SUCCESS): TaskDebugFile
    {
        return $this->csv([[
            'id' => $transactionId,
            'userId' => $userId,
            'type' => '',
            'opId' => '1',
            'status' => $status,
            'sum_usd' => '6.6306',
            'sum' => '593.5000',
            'sum_locked' => '',
            'currency' => 'USD',
            'paySys' => 'inner_pay',
            'requisite' => '',
            'dateCreated' => '2024-10-01 00:00:00',
            'dateSent' => '',
            'dateUpdated' => $dateUpdate,
            'balanceAfterChanges' => '3393.9400',
            'comment' => '',
            'commentPayment' => '',
            'platform' => '',
            'externalPaySystem' => 'innerpay',
            'processTransactionId' => 'e3c30c3d-3c7f-4dab-935f-558298bdc910',
            'accountId' => '',
            'useragent' => '',
            'refcode' => '',
            'ip' => '',
            'domain' => '',
            'balanceType' => 'real',
        ]]);
    }
}
