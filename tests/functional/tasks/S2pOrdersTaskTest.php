<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\config\tasks\Res;
use app\back\entities\S2pOrder;
use app\back\modules\task\actions\import\s2p\S2pOrdersTask;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pProjects;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(S2pOrdersTask::class)]
class S2pOrdersTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;
    use FakerCheckUnitTrait;

    public function testDontUpdateFirstSuccess(): void
    {
        $dateUpdate = '2024-10-01';
        $this->haveRates();

        $uuid = uniqid("", false);
        $userId = self::uniqRuntimeId();

        $this->runTask('s2p-orders', Res::S2P, $this->debugFile($this->getS2pOrdersContent($dateUpdate, $userId, $uuid)));

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $this->runTask('update-s2p-orders-firsts', Res::S2P, $dateUpdate, 'P1D');

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $dateUpdate = '2024-10-01 09:30:00';
        $this->runTask('s2p-orders', Res::S2P, $this->debugFile($this->getS2pOrdersContent($dateUpdate, $userId, $uuid)));

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);
    }

    public function testUpdateFirstSuccessAfterTransactionFail(): void
    {
        $dateUpdate = '2024-10-01';
        $this->haveRates();

        $uuid = uniqid("", false);
        $userId = self::uniqRuntimeId();

        $this->runTask('s2p-orders', Res::S2P, $this->debugFile($this->getS2pOrdersContent($dateUpdate, $userId, $uuid)));

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => false,
            'is_first_try' => false,
        ]);

        $this->runTask('update-s2p-orders-firsts', Res::S2P, $dateUpdate, 'P1D');

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => true,
            'is_first_try' => true,
        ]);

        $dateUpdate = '2024-10-01 09:30:00';
        $this->runTask('s2p-orders', Res::S2P, $this->debugFile($this->getS2pOrdersContent($dateUpdate, $userId, $uuid, S2pOrder::STATUSES[S2pOrder::STATUS_FAIL])));
        $this->runTask('update-s2p-orders-firsts', Res::S2P, $dateUpdate, 'P1D');

        $this->seeRecordWithFields(S2pOrders::class, [
            'id' => $uuid,
        ], [
            'is_first_success' => false,
            'is_first_try' => true,
        ]);
    }

    private function getS2pOrdersContent(string $dateUpdate, int $userId, string $uuid, string $status = S2pOrder::STATUSES[S2pOrder::STATUS_SUCCESS]): string
    {
        $s2pProject = $this->repo(S2pProjects::class)->findOne(['IS NOT', 'site_id', null]);
        $projectName = $s2pProject->name;

        return <<<DATA
        [
            {
                "uuid":"$uuid",
                "invoice":"1064287674",
                "status":"$status",
                "payment_context":"CTA0f4c0340bd529d4692b788d17d1a4",
                "payment_direction":"IN",
                "type":"initial",
                "payment_system":"tr_hizli_havale_no_amount",
                "pay_system_type":"common",
                "payment_method":"IN-tr_hizli_havale_no_amount-isettle",
                "payment_class":"isettle",
                "payer_identity":{"requisite":"empty","login":"trusted","user":"trusted","score":100},
                "status_detail":{"code":"0","message":"Success","visible":"1"},
                "requisites":
                {
                    "project_user":
                    {
                        "name":"$userId",
                        "is_payment":false,
                        "value":"ANNA"
                    }
                },
                "display_options":
                {
                    "success_url":"https:\/\/basari841.bet\/payment\/status-success?backUrl=\/",
                    "p_user_agent":"Mozilla\/5.0 (Linux; Android 10; K) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/129.0.0.0 Mobile Safari\/537.36",
                    "device":"mobile"
                },
                "mid_name":"iSettle Shntd Turk hizli_havale IN",
                "project":"$projectName",
                "created":"2024-10-01 00:00:00",
                "process_created":"2024-10-01 00:00:00.546828",
                "modified":"$dateUpdate",
                "payed":"2024-10-01 00:00:00",
                "client_original_amount":"300.00",
                "client_original_currency":"USD",
                "mid_principal_amount":"300.00000000000000000000",
                "mid_principal_currency_code":"USD",
                "pay_system_original_amount":"300.00",
                "pay_system_original_currency":"USD",
                "order_comment_general":null,
                "order_comment_pay_system":null,
                "ref_code":"mb_BQA5RQAA3HAAAAAlAQA.2024-08.19.junglemathch_dm_tr_fb4634xdb111xi9xt4xau5xscxl_bspwa",
                "x_branch":1
            }
        ]
DATA;
    }
}
