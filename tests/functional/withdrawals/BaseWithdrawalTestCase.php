<?php

declare(strict_types=1);

namespace app\tests\functional\withdrawals;

use app\back\entities\PaySystem;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\entities\WithdrawalRule;
use app\back\modules\finance\withdrawalsAutoProcessing\components\WithdrawalsAutoProcessor;
use app\back\modules\finance\withdrawalsAutoProcessing\components\WithdrawalsAutoProcessResult;
use app\back\repositories\PaySystems;
use app\back\repositories\UserMetrics;
use app\back\repositories\Withdrawals;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;
use Psr\Log\NullLogger;

class BaseWithdrawalTestCase extends TestCase
{
    use DbTransactionalUnitTrait;

    protected const string PENDING_SUM_ORIG = '100.00';

    protected readonly WithdrawalsAutoProcessor $withdrawalsAutoProcessor;
    protected readonly WithdrawalsAutoProcessResult $expected;
    protected readonly WithdrawalRule $rule;
    protected readonly int $employeeId;

    public function setUp(): void
    {
        $this->withdrawalsAutoProcessor = $this->container()->create(WithdrawalsAutoProcessor::class, [
            'log' => new NullLogger(),
        ]);
        $this->expected = new WithdrawalsAutoProcessResult();
        $this->rule = new WithdrawalRule();
        $this->rule->id = self::uniqRuntimeId();

        $this->haveRates('-1 day');

        $this->employeeId = $this->haveSystemUser()->employee_id;
    }

    protected function resultProcessRule(array $testRules): void
    {
        $from = new \DateTimeImmutable('-1 day');
        $to = new \DateTimeImmutable();

        $this->rule->site_id = Site::GGB;
        $this->rule->steps = implode("\n", $testRules);

        $this->withdrawalsAutoProcessor->setSystemUserId($this->employeeId);

        $res = $this->withdrawalsAutoProcessor->processRule($this->rule, $from, $to, false);

        // because db sorted strings ['8','9','10'] => ['10', '8','9']
        usort($res->logs, static fn($rowA, $rowB) => $rowA['transaction_id'] <=> $rowB['transaction_id']);

        self::assertSame(get_object_vars($this->expected), get_object_vars($res));
    }

    protected function haveUserStatRecordOut(User $user, string $createdAt = '-1 hour', string $exactPaySys = '', int $status = UserTransaction::STATUS_NEW, ?string $comment = null): UserTransaction
    {
        if ($exactPaySys) {
            /** @var PaySystem $paySys */
            $paySys = $this->haveRecord(PaySystems::class, [
                'name' => $exactPaySys,
            ]);
        }

        return $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'user_id' => $user->user_id,
            'created_at' => new \DateTimeImmutable($createdAt),
            'op_id' => UserTransaction::OP_OUT,
            'status' => $status,
            'amount_orig' => self::PENDING_SUM_ORIG,
            'wallet' => (string)mt_rand(),
            'currency' => Rate::RUB,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_OUT,
            'pay_sys_id' => $exactPaySys ? $paySys->id : mt_rand(),
            'comment' => $comment,
        ]);
    }

    protected function haveUserStatRecordIn(User $user, UserTransaction $userStat): UserTransaction
    {
        return $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'user_id' => $user->user_id,
            'created_at' => new \DateTimeImmutable('-2 hour'),
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'wallet' => $userStat->wallet,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_IN,
        ]);
    }

    protected function haveMetric(User $user, array $metric, mixed $metricValue): void
    {
        [$metricId, $column] = $metric;

        $this->haveRecord(UserMetrics::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'metric' => $metricId,
            $column => $metricValue
        ]);
    }

    protected function haveWithdrawalRecord(UserTransaction $stat, int $status): void
    {
        $this->haveRecord(Withdrawals::class, [
            'site_id' => $stat->site_id,
            'transaction_id' => $stat->transaction_id,
            'user_id' => $stat->user_id,
            'status' => $status,
            'decision' => Withdrawal::DECISION_DENY,
            'operator_id' => $this->employeeId,
        ]);
    }
}
