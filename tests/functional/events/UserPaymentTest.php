<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\PaySystem;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\modules\events\events\UserPaymentEvent;
use app\back\repositories\PaySystems;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserPaymentEvent::class)]
class UserPaymentTest extends BaseEventTestCase
{
    public function testTaskUsersStats(): void
    {
        $this->haveRates();
        $this->haveRates();

        $user1 = $this->haveUserRecord(['site_id' => Site::GGB]);
        $user2 = $this->haveUserRecord(['site_id' => Site::GGB]);
        $user3 = $this->haveUserRecord(['site_id' => Site::GGB]);

        $stat1Before = $this->newUserStatRecordCustom($user1, '100.00', '-1 day');
        $stat1 = $this->newUserStatRecordCustom($user1, '250.00', 'now');
        $stat2Before = $this->newUserStatRecordCustom($user2, '100.00', '-1 day');
        $stat2 = $this->newUserStatRecordCustom($user2, '350.00', 'now');
        $stat3 = $this->newUserStatRecordCustom($user3, '199.00', 'now');

        $this->haveUserTransactionRecord((array)$stat1Before);
        $this->haveUserTransactionRecord((array)$stat2Before);

        $subscription = $this->haveSubscription('crm', 'user_payments', [
            'site_id' => Site::GGB,
            'op_id' => UserTransaction::OP_IN,
            'summ_rub_min' => 200,
            'summ_rub_max' => 300,
            'is_first' => false,
        ]);

        MockServer::with(function () use ($stat1, $stat2, $stat3) {
            $this->runTask('users-stats', Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([
                $this->statRecordToTaskCsv($stat1),
                $this->statRecordToTaskCsv($stat2),
                $this->statRecordToTaskCsv($stat3),
            ]));
            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => $stat1->site_id,
                'user_id' => $stat1->user_id,

                'summ_orig' => $stat1->amount_orig,
                'amount_orig' => $stat1->amount_orig,
                'curr_orig' => $stat1->currency,
                'currency' => $stat1->currency,
                'stats_id' => $stat1->transaction_id,
                'transaction_id' => $stat1->transaction_id,
                'pay_sys_id' => $stat1->pay_sys_id
            ]])
        ]);
    }

    public function testUsersStatsAdminCommentFromSmen(): void
    {
        $this->haveRates();
        $this->haveRates();

        $site = Site::CV;

        $reasons = [
            // Valid
            "General error",

            // Invalids
            "Reject reason: Customer cancellation/Client lost",
            null,
            '',
            'Pay system error',
        ];

        $users = array_map(fn () => $this->haveUserRecord(['site_id' => $site]), range(1, count($reasons)));
        $stats = array_map(fn ($u, $r) => $this->newUserStatRecordCustom($u, '100.00', 'now', $r), $users, $reasons);
        $expectedStat = $stats[0];

        $subscription = $this->haveSubscription('crm', 'user_payments', [
            'site_id' => $site,
            'op_id' => UserTransaction::OP_IN,
            'comment_admin' => ["General", "Reject reason: Pay system error."],
        ]);

        MockServer::with(function () use ($stats) {
            $this->runTask('users-stats', Res::CV, self::LAST_MINUTE_PERIOD, $this->csv(array_map($this->statRecordToTaskCsvSmen(...), $stats)));
            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => $expectedStat->site_id,
                'user_id' => $expectedStat->user_id,
                'summ_orig' => $expectedStat->amount_orig,
                'amount_orig' => $expectedStat->amount_orig,
                'curr_orig' => $expectedStat->currency,
                'currency' => $expectedStat->currency,
                'stats_id' => $expectedStat->transaction_id,
                'transaction_id' => $expectedStat->transaction_id,
                'pay_sys_id' => $expectedStat->pay_sys_id
            ]])
        ]);
    }

    private function newUserStatRecordCustom(User $user, string $sumRub, string $time, ?string $adminComment = ''): UserTransaction
    {
        /** @var PaySystem $paySys */
        $paySys = $this->haveRecord(PaySystems::class, ['name' => 'bank_cards_visa']);

        return new UserTransaction([
            'transaction_id' => (string) self::uniqRuntimeId(),
            'user_id' => $user->user_id,
            'site_id' => $user->site_id,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'amount_orig' => $sumRub,
            'currency' => Rate::RUB,
            'created_at' => new \DateTimeImmutable($time),
            'dir' => UserTransaction::DIR_IN,
            'pay_sys_id' => $paySys->id,
            'comment_admin' => $adminComment,
        ]);
    }

    private function statRecordToTaskCsv(UserTransaction $stat): array
    {
        return [
            'player_id' => $stat->user_id,
            'dir' => match ($stat->dir) {
                UserTransaction::DIR_IN => 'IN'
            },
            'status' => $stat->status,
            'sum' => $stat->amount_orig,
            'currency' => $stat->currency,
            'created_at' => $stat->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $stat->created_at->format('Y-m-d H:i:s'),
            'local_id' => $stat->transaction_id,
            'method' => 'bank_cards_visa'
        ];
    }

    private function statRecordToTaskCsvSmen(UserTransaction $stat): array
    {
        return [
            'userId' => $stat->user_id,
            'type' => match ($stat->dir) {
                UserTransaction::DIR_IN => 'IN'
            },
            'status' => $stat->status,
            'sum' => $stat->amount_orig,
            'currency' => $stat->currency,
            'dateCreated' => $stat->created_at->format('Y-m-d H:i:s'),
            'dateUpdated' => $stat->created_at->format('Y-m-d H:i:s'),
            'id' => $stat->transaction_id,
            'paySys' => 'bank_cards_visa',
            'commentPayment' => $stat->comment_admin
        ];
    }
}
