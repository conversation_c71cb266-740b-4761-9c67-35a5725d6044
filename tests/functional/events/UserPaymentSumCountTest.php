<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\events\events\UserPaymentSumCountEvent;
use app\back\repositories\UserTransactions;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserPaymentSumCountEvent::class)]
class UserPaymentSumCountTest extends BaseEventTestCase
{
    public function testBasic(): void
    {
        $siteId = Site::CV;
        $userIds = array_map(self::uniqRuntimeId(...), range(0, 4));

        $subscription = $this->haveSubscription('crm', 'users_payments_aggregated', [
            'site_id' => $siteId,
            'op_id' => UserTransaction::OP_IN,
            'summ_min' => 100,
            'summ_max' => 200,
        ]);

        $this->haveRates();

        MockServer::with(function () use ($siteId, $userIds) {
            $transactions = [
                $this->newUserStatRecordCustom($siteId, $userIds[0], 50), // Not enough money

                $this->newUserStatRecordCustom($siteId, $userIds[1], 150),

                $this->newUserStatRecordCustom($siteId, $userIds[2], 50),
                $this->newUserStatRecordCustom($siteId, $userIds[2], 50),
                $this->newUserStatRecordCustom($siteId, $userIds[2], 50),

                $this->newUserStatRecordCustom($siteId, $userIds[3], 200), // More than max limit, checking < op

                $this->newUserStatRecordCustom($siteId, $userIds[4], 150), // Total sum more than max limit
                $this->newUserStatRecordCustom($siteId, $userIds[4], 100),
            ];

            $this->runTask('users-stats', Res::CV, self::LAST_MINUTE_PERIOD, $this->csv(array_map($this->statRecordToTaskCsvSmen(...), $transactions)));
            $this->sendQueuedEvents();
        }, [
            [$this->eventRequest($subscription, [
                ['site_id' => $siteId, 'user_id' => $userIds[1]],
                ['site_id' => $siteId, 'user_id' => $userIds[2]],
            ]), null],
        ]);
    }

    private function newUserStatRecordCustom(int $siteId, int $userId, int $sumUSD): UserTransaction
    {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return $this->repo(UserTransactions::class)->validateAndCreate([
            'transaction_id' => (string)self::uniqRuntimeId(),
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => '2024-11-05',
            'updated_at' => '2024-11-05',
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'amount_orig' => (string)$sumUSD,
            'currency' => Rate::USD,
        ]);
    }

    private function statRecordToTaskCsvSmen(UserTransaction $stat): array
    {
        return [
            'id' => $stat->transaction_id,
            'userId' => $stat->user_id,
            'opId' => $stat->op_id,
            'status' => $stat->status,
            'sum' => $stat->amount_orig,
            'currency' => $stat->currency,
            'dateCreated' => $stat->created_at->format('Y-m-d H:i:s'),
            'dateUpdated' => $stat->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
