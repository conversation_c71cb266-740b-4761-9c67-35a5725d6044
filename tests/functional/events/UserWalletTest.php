<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserWallet;
use app\back\modules\events\events\UserBalanceEvent;
use app\back\modules\events\events\UserWalletEvent;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserWalletEvent::class)]
#[CoversClass(UserBalanceEvent::class)]
class UserWalletTest extends BaseEventTestCase
{
    public function testTaskUsersWalletsReal(): void
    {
        $this->testEvent('users-wallets-real', UserWallet::TYPE_REAL);
    }

    public function testTaskUsersWalletsBonus(): void
    {
        $this->testEvent('users-wallets-bonus', UserWallet::TYPE_BONUS);
    }

    private function testEvent(string $task, int $balanceType): void
    {
        $this->haveRates();

        $subscription1 = $this->haveSubscription('crm', 'users_wallets', [
            'site_id' => Site::GGB,
            'type' => $balanceType,
            'balance_rub_from' => 100,
            'balance_rub_to' => 200,
        ]);

        $userWallet1 = $this->newUserWalletRecordCustom($balanceType, 109.82);
        $userWallet2 = $this->newUserWalletRecordCustom($balanceType, 209.82);

        $expected = [
            $this->eventRequest($subscription1, [[
                'site_id' => $userWallet1->site_id,
                'user_id' => $userWallet1->user_id,
            ]])
        ];

        $dataCount = $workerCount = $sendsCount = 1;

        if ($balanceType === UserWallet::TYPE_REAL) {
            $subscription2 = $this->haveSubscription('crm', 'user_balances', [
                'site_id' => Site::GGB,
                'balance_from' => 100,
                'balance_to' => 200,
            ]);

            $expected[] = $this->eventRequest($subscription2, [
                ['site_id' => $userWallet1->site_id, 'user_id' => $userWallet1->user_id],
            ]);

            $dataCount = $workerCount = $sendsCount = 2;
        }

        MockServer::with(function () use ($task, $userWallet1, $userWallet2, $dataCount, $workerCount, $sendsCount) {
            $this->runTask($task, Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([
                $this->userWalletToTaskCsv($userWallet1),
                $this->userWalletToTaskCsv($userWallet2),
            ]));
            $this->sendQueuedEvents($dataCount, $workerCount, $sendsCount);
        }, $expected);
    }

    private function userWalletToTaskCsv(UserWallet $userWallet): array
    {
        return [
            'player_id' => $userWallet->user_id,
            'balance_id' => $userWallet->wallet_id,
            'amount' => $userWallet->balance,
            'currency' => $userWallet->currency,
            'updated_at' => date('Y-m-d\TH:i:s.vP'),
        ];
    }

    private function newUserWalletRecordCustom(int $balanceType, float $amount): UserWallet
    {
        return new UserWallet([
            'site_id' => Site::GGB,
            'user_id' => mt_rand(),
            'wallet_id' => mt_rand(),
            'currency' => Rate::RUB,
            'balance' => (string) $amount,
            'type' => $balanceType,
        ]);
    }
}
