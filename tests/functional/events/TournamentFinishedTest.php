<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\UserTournament;
use app\back\modules\events\events\TournamentFinishedEvent;
use app\back\repositories\UserTournaments;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(TournamentFinishedEvent::class)]
class TournamentFinishedTest extends BaseEventTestCase
{
    public function testBasic(): void
    {
        $siteId = Site::CV;

        $tId = self::uniqRuntimeId();
        $tId2 = self::uniqRuntimeId();
        $tId3 = self::uniqRuntimeId();
        $tTitle = 'US Elections';

        $subscription = $this->haveSubscription('crm', 'tournament_finished', [
            'site_id' => $siteId,
            'medal_place' => [1, 3],
            'tournament_id' => [$tId, $tId2],
        ]);

        $ut1 = $this->getUt($siteId, $tId, 1);
        $ut2 = $this->getUt($siteId, $tId, 3);
        $this->getUt(Site::ARM, $tId, 1); // Another site
        $this->getUt($siteId, $tId, 2); // Medal place mismatch
        $this->getUt($siteId, $tId, null); // No medal place
        $this->getUt($siteId, $tId2, 1); // Second not completed tournament
        $this->getUt($siteId, $tId3, 1); // Another tournament, not in subscription

        $this->haveRates();
        MockServer::with(function () use ($tId, $tId2, $tId3, $tTitle) {
            $this->runTask('tournaments', Res::CV, self::LAST_MINUTE_PERIOD, $this->csv([
                ['tournamentId' => $tId, 'title' => $tTitle, 'status' => 'completed', 'dateStart' => '2024-11-05'],
                ['tournamentId' => $tId2, 'title' => $tTitle, 'status' => 'coming', 'dateStart' => '2024-11-05'],
                ['tournamentId' => $tId3, 'title' => $tTitle, 'status' => 'completed', 'dateStart' => '2024-11-05'],
            ]));

            $this->sendQueuedEvents();
        }, [
            [$this->eventRequest($subscription, [
                ['site_id' => $ut1->site_id, 'user_id' => $ut1->user_id, 'title' => $tTitle],
                ['site_id' => $ut2->site_id, 'user_id' => $ut2->user_id, 'title' => $tTitle],
            ]), null],
        ]);
    }

    private function getUt(int $siteId, int $tId, ?int $medalPlace): UserTournament
    {
        /** @var UserTournament $ut */
        $ut = $this->haveRecord(UserTournaments::class, [
            'site_id' => $siteId,
            'tournament_id' => $tId,
            'created_at' => '2024-11-06',
            'updated_at' => '2024-11-06',
            'user_id' => self::uniqRuntimeId(),
            'medal_place' => $medalPlace,
        ]);

        return $ut;
    }
}
