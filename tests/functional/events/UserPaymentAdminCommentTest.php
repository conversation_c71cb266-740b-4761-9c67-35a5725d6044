<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\components\RuntimeCache;
use app\back\config\tasks\Res;
use app\back\entities\PaySystem;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\modules\events\events\UserPaymentAdminCommentEvent;
use app\back\repositories\PaySystems;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserPaymentAdminCommentEvent::class)]
class UserPaymentAdminCommentTest extends BaseEventTestCase
{
    public function testTaskUsersStatsAdminComments(): void
    {
        $siteId = Site::GGB;
        $siteId2 = Site::VV;
        $transactionIds = [
            (string) self::uniqRuntimeId(),
            (string) self::uniqRuntimeId(),
            (string) self::uniqRuntimeId(),
            (string) self::uniqRuntimeId(),
        ];
        $reasons = [
            "Reject reason: Customer cancellation/Client lost",
            "Reject reason: Pay system error. Technical problems on payment systems side",
            "Suspended",
        ];

        /** @var PaySystem $paySys */
        $paySys = $this->haveRecord(PaySystems::class, ['name' => 'bank_cards_visa']);

        $this->haveRates();

        $user1 = $this->haveUserRecord(['site_id' => $siteId]);
        $sum1 = '10.10';

        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'transaction_id' => $transactionIds[0],
            'user_id' => $user1->user_id,
            'amount_orig' => $sum1,
            'currency' => Rate::EUR,
            'pay_sys_id' => $paySys->id,
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'transaction_id' => $transactionIds[1],
            'user_id' => $user1->user_id,
            'comment_admin' => $reasons[1],
            'comment_admin_updated_at' => new \DateTimeImmutable('-1 day'),
            'amount_orig' => '133.15',
            'currency' => Rate::USD,
            'pay_sys_id' => $paySys->id,
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'transaction_id' => $transactionIds[2],
            'user_id' => $user1->user_id,
            'amount_orig' => '2000',
            'currency' => Rate::RUB,
            'pay_sys_id' => $paySys->id,
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => $siteId2,
            'transaction_id' => $transactionIds[3],
            'user_id' => $user1->user_id,
            'amount_orig' => '100.00',
            'currency' => Rate::EUR,
            'pay_sys_id' => $paySys->id,
        ]);

        $subscription = $this->haveSubscription('crm', 'users_stats_admin_comment', [
            'site_id' => $siteId,
            'comment_admin' => ["Reject reason: Customer ", "Reject reason: Pay system error."],
        ]);

        MockServer::with(function () use ($transactionIds, $reasons) {
            $this->runTask('users-stats-admin-comments', Res::GGB, self::LAST_MINUTE_PERIOD, $this->csv([
                [
                    'transactionLocalId' => $transactionIds[0],
                    'comment' => $reasons[0],
                    'createdAt' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                ],
                [
                    'transactionLocalId' => $transactionIds[1],
                    'comment' => $reasons[1],
                    'createdAt' => (new \DateTimeImmutable('-1 day'))->format('Y-m-d H:i:s'),
                ],
                [
                    'transactionLocalId' => $transactionIds[2],
                    'comment' => $reasons[2],
                    'createdAt' => (new \DateTimeImmutable('now'))->format('Y-m-d H:i:s'),
                ],
            ]));

            RuntimeCache::clear();

            $this->runTask('users-stats-admin-comments', Res::VV, self::LAST_MINUTE_PERIOD, $this->csv([[
                'transactionLocalId' => $transactionIds[3],
                'comment' => $reasons[1],
                'createdAt' => (new \DateTimeImmutable('-1 day'))->format('Y-m-d H:i:s'),
            ]]));
            $this->sendQueuedEvents(2, 2, 1);
        }, [
            $this->eventRequest($subscription, [
                [
                    'site_id' => $siteId,
                    'user_id' => $user1->user_id,
                    'amount_orig' => $sum1,
                    'summ_orig' => $sum1,
                    'currency' => Rate::EUR,
                    'curr_orig' => Rate::EUR,
                    'transaction_id' => $transactionIds[0],
                    'stats_id' => $transactionIds[0],
                    'pay_sys_id' => $paySys->id,
                ]
            ])
        ]);
    }
}
