<?php

declare(strict_types=1);

namespace app\tests\functional\events;

use app\back\config\tasks\Res;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\events\events\UserFirstDepEvent;
use app\tests\libs\mock\MockServer;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UserFirstDepEvent::class)]
class UserFirstDepTest extends BaseEventTestCase
{
    public function testTaskUpdateUsersStats(): void
    {
        $this->haveRates();
        $this->haveRates();

        $stat1 = $this->haveUserStatRecordCustom('now', ['amount_orig' => '100']);
        $stat2 = $this->haveUserStatRecordCustom('-1 day', ['amount_orig' => '10', 'is_first_success' => true]);
        $this->haveUserStatRecordCustom('now', ['amount_orig' => '100', 'user_id' => $stat2->user_id]);

        $subscription = $this->haveSubscription('crm', 'users_first_dep', [
            'site_id' => Site::GGB,
        ]);

        MockServer::with(function () {
            $this->runTask('update-users-transactions-firsts', Res::GGB);

            $this->sendQueuedEvents();
        }, [
            $this->eventRequest($subscription, [[
                'site_id' => $stat1->site_id,
                'user_id' => $stat1->user_id,
            ]])
        ]);
    }

    private function haveUserStatRecordCustom(string $date, array $attributes = []): UserTransaction
    {
        return $this->haveUserTransactionRecord([
            'site_id' => Site::GGB,
            'op_id' => UserTransaction::OP_IN,
            'status' => UserTransaction::STATUS_SUCCESS,
            'currency' => Rate::RUB,
            'created_at' => new \DateTimeImmutable($date),
            'updated_at' => new \DateTimeImmutable($date),
            'dir' => UserTransaction::DIR_IN,
            ...$attributes
        ]);
    }
}
