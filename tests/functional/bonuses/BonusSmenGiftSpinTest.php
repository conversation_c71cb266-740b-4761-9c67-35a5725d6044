<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\UuidHelper;
use app\back\entities\BonusLog;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserWallet;
use app\back\modules\finance\components\bonus\BonusDictionarySmen;
use app\back\modules\finance\components\bonus\forms\BonusSmenGiftSpinsForm;
use app\back\modules\finance\components\bonus\WebBonusForm;
use app\back\modules\task\requests\SmenRequest;
use app\back\repositories\Users;
use app\back\repositories\UserWallets;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\ParametricUri;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Monolog\Test\TestCase;
use Nyholm\Psr7\Request;
use PHPUnit\Framework\Attributes\CoversClass;
use Psr\SimpleCache\CacheInterface;

#[
    CoversClass(BonusSmenGiftSpinsForm::class),
    CoversClass(SmenRequest::class),
    CoversClass(WebBonusForm::class),
]
class BonusSmenGiftSpinTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;
    use SmenSenderTestTrait;

    public function testSendBonusPlayerCard(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $siteId = Site::CV;
        $userId = self::uniqRuntimeId();
        $currency = Rate::RUB;
        $employee = $this->haveEmployee();
        $this->haveRates();

        $user = $this->haveUserRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $this->haveRecord(UserWallets::class, [
            'site_id' => $siteId, 'user_id' => $userId, 'currency' => $currency, 'wallet_id' => self::uniqRuntimeId(), 'type' => UserWallet::TYPE_BONUS
        ]);

        MockServer::with(function () use ($user, $employee) {
            $form = $this->container()->get(WebBonusForm::class);
            self::consoleOutputToDebug(function () use ($form, $user, $employee) {
                $bonusType = BonusDictionarySmen::SMEN_GIFT_SPINS;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string)$user->user_id,
                    'remoteBonusType' => $bonusType,
                ]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_FILL;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string)$user->user_id,
                    'remoteBonusType' => $bonusType,
                    'values' => [
                        'games' => [0 => 1382, 1 => 1439],
                        'quantity' => 10,
                        'wagerHours' => 960,
                        'userId' => (string)$user->user_id,
                    ],
                ]);
                /** @var BonusSmenGiftSpinsForm $spinForm */
                $spinForm = $form->bonusForm;
                $this->assertSame([100 => '100 RUB', 200 => '200 RUB'], $spinForm->betsInMoneyList());

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string)$user->user_id,
                    'remoteBonusType' => $bonusType,
                    'values' => [
                        'games' => [0 => 1382, 1 => 1439],
                        'quantity' => 10,
                        'wager' => 5,
                        'userId' => (string)$user->user_id,
                        'betsInMoney' => '100',
                        'wagerHours' => 960,
                        'ignoreProductMaxBet' => false,
                        'ignoreProductMaxTransfer' => false,
                        'adminComment' => "2_bonus_test",
                        'idempotenceId' => UuidHelper::v5(UuidHelper::nil(), '2_bonus_test'),
                    ],
                ]);
                $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);

                //allow to assign bonuses with BBL_STATUS_MANUAl in advancedMode
                $this->db()->createCommand()->update(Users::TABLE_NAME, [
                    'bbl_status' => User::BONUS_BL_STATUS_YES_MANUAL])->execute();
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string)$user->user_id,
                    'remoteBonusType' => $bonusType,
                ]);
                //forbidden to assign bonuses with BBL_STATUS_AUTO in advancedMode
                $this->db()->createCommand()->update(Users::TABLE_NAME, [
                    'bbl_status' => User::BONUS_BL_STATUS_YES_AUTO])->execute();
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $this->expectException(InvalidException::class);
                $this->expectExceptionMessage('{
    "message": "UserId Line 1: user 1 is in bonus BL"
}');
                $form->validateOrException([
                    'siteId' => $user->site_id,
                    'userId' => (string)$user->user_id,
                    'remoteBonusType' => $bonusType,
                ]);
            });
        }, [
            [
                $this->bonusListRequest($siteId),
                $this->bonusListResponse(),
            ],
            [
                new Request(
                    'POST',
                    new ParametricUri('/api/analytics/bonuses/submit-bonus-form', ['signature' => '039a7b73f4c7cd7d3f44b62eea57702f']),
                    [],
                    new FormBodyStream([
                        'user_ids'  => [$userId],
                        'operator_email' => $employee->email,
                        'admin_comment' => '2_bonus_test',
                        'prize' => [
                            'type' => BonusDictionarySmen::SMEN_GIFT_SPINS,
                            'games_ids' => [1382, 1439],
                            'count' => 10,
                            'wager' => 5,
                            'bet' => 100,
                            'spin_type' => BonusDictionarySmen::SMEN_GIFT_SPINS,
                            'days' => 1,
                            'ignore_max_transfer' => 0,
                            'wagering_hours' => 960,
                        ],
                    ])
                ),
                $this->bonusSuccessResponse(),
            ],
        ]);
    }
}
