<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\components\helpers\UuidHelper;
use app\back\entities\BonusLog;
use app\back\entities\Site;
use app\back\modules\finance\components\bonus\BonusDictionarySmen;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\finance\components\bonus\WebBonusForm;
use app\back\repositories\BonusLogs;
use app\back\repositories\UserWallets;
use app\tests\libs\mock\FormBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\mock\ParametricUri;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Nyholm\Psr7\Request;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

class BonusSmenLoyaltyMultiplyTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;
    use SmenSenderTestTrait;

    private const int SITE_ID = Site::GMSD;
    private const string REMOTE_BONUS = BonusDictionarySmen::SMEN_LOYALTY_MULTIPLY;

    public function testSendManualBonuses(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $userId = self::uniqRuntimeId();
        $currency = self::randomCurrency();
        $employee = $this->haveEmployee();
        $this->haveRates();

        $user = $this->haveUserRecord(['site_id' => self::SITE_ID, 'user_id' => $userId]);
        $this->haveRecord(UserWallets::class, [
            'site_id' => self::SITE_ID, 'user_id' => $user->user_id,  'currency' => $currency, 'wallet_id' => self::uniqRuntimeId()
        ]);

        MockServer::with(function () use ($user, $employee) {
            $form = $this->container()->get(WebBonusForm::class);
            self::consoleOutputToDebug(static function () use ($form, $user, $employee) {
                $form->hasUserInForm = false;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException(['siteId' => self::SITE_ID, 'remoteBonusType' => self::REMOTE_BONUS]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException(self::x2BonusFormSettings((string) $user->user_id, 10));
                $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);
            });
        }, [
            [
                $this->bonusListRequest(self::SITE_ID),
                $this->bonusListResponse(),
            ],
            [
                $this->sendBonusRequest($userId, $employee->email, 10, '1efc851fd9d3237fea6c5496c3dcbe03'),
                $this->bonusSuccessResponse(),
            ]
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $user->user_id], [
            'sum' => null,
            'currency' => $currency,
            'type_id' => BonusLog::TYPE_MANUAL_BONUS,
            'bonus' => self::REMOTE_BONUS,
            'bonus_type' => BonusTypes::TYPE_LOYALTY_MULTIPLY,
            'admin_comment' => '9_test',
            'operator_id' => $employee->employee_id,
            'extra' => [
                'adminComment' => '9_test',
                'currency' => $currency,
                'minutes' => 10,
                'multiplyType' => 'x2',
                'userId' => '1',
            ],
        ]);
    }

    public function testMultiAssign(): void
    {
        $this->container()->get(CacheInterface::class)->clear();

        //need static email because self::uniqRuntimeId() change $signature in $this->sendAddMoneyBonusRequest
        $employee = $this->haveEmployee(['email' => '<EMAIL>']);
        $this->haveRates();

        $currency = self::randomCurrency();
        [$userId1Ok, $userId2Ok, $userId3Bad]  = [self::uniqRuntimeId(), self::uniqRuntimeId(), self::uniqRuntimeId()];
        $this->haveUserRecord(['user_id' => $userId1Ok, 'site_id' => self::SITE_ID]);
        $this->haveUserRecord(['user_id' => $userId2Ok, 'site_id' => self::SITE_ID]);
        $this->haveUserRecord(['user_id' => $userId3Bad, 'site_id' => self::SITE_ID]);

        $this->haveRecords(UserWallets::class, [
            ['site_id' => self::SITE_ID, 'user_id' => $userId1Ok, 'currency' => $currency, 'wallet_id' => self::uniqRuntimeId()],
            ['site_id' => self::SITE_ID, 'user_id' => $userId2Ok, 'currency' => $currency, 'wallet_id' => self::uniqRuntimeId()],
            ['site_id' => self::SITE_ID, 'user_id' => $userId3Bad, 'currency' => $currency, 'wallet_id' => self::uniqRuntimeId()]
        ]);

        MockServer::with(function () use ($employee, $userId1Ok, $userId2Ok, $userId3Bad) {

            $form = $this->container()->get(WebBonusForm::class);

            self::consoleOutputToDebug(function () use ($form, $employee, $userId1Ok, $userId2Ok, $userId3Bad) {
                $multiAssignString = "$userId1Ok\n$userId2Ok\n$userId3Bad";
                $form->hasUserInForm = false;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException(['siteId' => self::SITE_ID, 'remoteBonusType' => self::REMOTE_BONUS]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException(self::x2BonusFormSettings($multiAssignString, 10));

                $sendError = $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);
                $expected =  $userId3Bad . ': {
    "message": "Remote validation error (480): Unknown validation error"
}';
                $this->assertSame($expected, $sendError);

                $this->assertSame((string)$userId3Bad, $form->bonusForm->userId);
            });
        }, [
            [
                $this->bonusListRequest(self::SITE_ID),
                $this->bonusListResponse(),
            ],
            [
                $this->sendBonusRequest($userId1Ok, $employee->email, 10, 'af708d4f974a84cd53fc7840aaa0d91a'),
                $this->bonusSuccessResponse(),
            ],
            [
                $this->sendBonusRequest($userId2Ok, $employee->email, 10, '2267ad56d0cab89c3b0ebbed06d903ee'),
                $this->bonusSuccessResponse(),
            ],
            [
                $this->sendBonusRequest($userId3Bad, $employee->email, 10, '4812d9acf5fcc6ec2e6535e130a8db00'),
                $this->bonusValidationErrorResponse(),
            ],
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId1Ok], [
            'sum' => null,
            'currency' => $currency,
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId2Ok], [
            'sum' => null,
            'currency' => $currency,
        ]);

        $this->dontSeeRecord(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId3Bad]);
    }

    private function sendBonusRequest(int $userId, string $employeeEmail, int $min, string $signature): Request
    {
        return new Request(
            'POST',
            new ParametricUri('/api/analytics/bonuses/submit-bonus-form', ['signature' => $signature]),
            [],
            new FormBodyStream(
                [
                'user_ids'  => [$userId],
                'operator_email' => $employeeEmail,
                'admin_comment' => '9_test',
                'prize' => [
                    'type' => BonusDictionarySmen::SMEN_LOYALTY_MULTIPLY,
                    'minutes' => $min,
                    'multiply_type' => 'x2',
                ],
                ]
            )
        );
    }

    private static function x2BonusFormSettings(string $userIds, int $min): array
    {
        return [
            'siteId' => self::SITE_ID,
            'remoteBonusType' => self::REMOTE_BONUS,
            'values' => [
                'userId' => $userIds,
                'multiplyType' => 'x2',
                'minutes' => $min,
                'adminComment' => '9_test',
                'idempotenceId' => UuidHelper::v5(UuidHelper::nil(), '1_bonus_test'),
            ],
        ];
    }
}
