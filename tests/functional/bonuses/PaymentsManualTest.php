<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\entities\BonusLog;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\finance\paymentsManual\PaymentManualForm;
use app\back\repositories\BonusLogs;
use app\back\repositories\UserWallets;
use app\tests\libs\mock\JsonBodyStream;
use app\tests\libs\mock\MockServer;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Nyholm\Psr7\Request;
use Nyholm\Psr7\Response;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

class PaymentsManualTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    public function testMultiAssign(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $siteId = Site::CV;
        [$userOk, $userBad] = [self::uniqRuntimeId(), self::uniqRuntimeId()];
        $currency = 'EUR';
        $this->haveRates();

        $this->haveUserRecord(['user_id' => $userOk, 'site_id' => $siteId]);
        $this->haveUserRecord(['user_id' => $userBad, 'site_id' => $siteId]);

        $this->haveRecords(UserWallets::class, [[
            'site_id' => $siteId,
            'user_id' => $userOk,
            'wallet_id' => self::uniqRuntimeId(),
            'type' => UserWallet::TYPE_REAL,
            'currency' => $currency,
        ], [
            'site_id' => $siteId,
            'user_id' => $userBad,
            'wallet_id' => self::uniqRuntimeId(),
            'type' => UserWallet::TYPE_REAL,
            'currency' => $currency,
        ]]);

        // $userOk to succeed, $userBad to fail on product
        $testString = "$userOk 100\n$userBad 200";

        $form = $this->container()->create(PaymentManualForm::class, [
            'auth' => null,
            'sessionMessages' => null,
            'checkAllowedSites' => false,
        ]);
        $form->setUuid(md5('test'));
        $form->validateOrException([
            'siteId' => $siteId,
            'userId' => $testString,
            'type' => UserTransaction::OP_ADMIN_IN_COMPENSATION,
            'comment' => 'test',
        ]);

        $sendErrors = null;
        MockServer::with(static function () use ($form, &$sendErrors) {
            self::consoleOutputToDebug(static function () use ($form, &$sendErrors) {
                $sendErrors = $form->send();
            });
        }, [
            [
                new Request('POST', '/api/payment/create/compensation/1/100/EUR/098f6bcd4621d373cade4e832627b4f6?signature=ec4b6b40b0e11ac1d7eee6bbdb1afdcd', [], 'comment=test'),
                new Response(200, [], new JsonBodyStream([
                    'success' => true,
                    'message' => null,
                ]))
            ],
            [
                new Request('POST', '/api/payment/create/compensation/2/200/EUR/098f6bcd4621d373cade4e832627b4f6?signature=eaef74c3109aff3c556280d537f19ad0', [], 'comment=test'),
                new Response(500, [], new JsonBodyStream([
                    'success' => false,
                    'message' => 'Internal server error',
                ]))
            ],
        ]);

        // userOk succeed
        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => $siteId, 'user_id' => $userOk], [
            'sum' => '100.00',
            'currency' => $currency,
            'type_id' => BonusLog::TYPE_MANUAL_PAYMENT,
            'bonus' => UserTransaction::OPERATIONS[UserTransaction::OP_ADMIN_IN_COMPENSATION],
            'bonus_type' => BonusTypes::TYPE_MONEY,
            'extra' => ['comment' => 'test'],
        ]);

        // $userBad failed on product
        $expected = $userBad . ": Response code: 500";
        $this->assertStringContainsString($expected, (string)$sendErrors);
        $this->dontSeeRecord(BonusLogs::class, ['site_id' => $siteId, 'user_id' => $userBad]);

        // userOk is removed from form, failed user kept
        $this->assertSame("$userBad 200", $form->userId);
    }
}
