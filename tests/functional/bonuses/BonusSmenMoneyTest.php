<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\entities\BonusLog;
use app\back\entities\Site;
use app\back\modules\finance\components\bonus\BonusDictionarySmen;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\finance\components\bonus\forms\BonusSmenMoneyForm;
use app\back\modules\finance\components\bonus\RestrictionManager;
use app\back\modules\finance\components\bonus\WebBonusForm;
use app\back\modules\task\requests\SmenRequest;
use app\back\repositories\BonusLogs;
use app\back\repositories\UserWallets;
use app\tests\libs\mock\MockServer;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

#[
    CoversClass(SmenRequest::class),
    CoversClass(BonusSmenMoneyForm::class),
    CoversClass(WebBonusForm::class),
]
class BonusSmenMoneyTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;
    use SmenSenderTestTrait;

    private const int SITE_ID = Site::CV;
    private const string REMOTE_BONUS = BonusDictionarySmen::SMEN_MONEY;
    private const string CURRENCY = RestrictionManager::CURRENCY; // used in signature

    public function testSendManualBonuses(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $userId = self::uniqRuntimeId();
        $employee = $this->haveEmployee();
        $this->haveRates();

        $user = $this->haveUserRecord(['site_id' => self::SITE_ID, 'user_id' => $userId]);

        $this->haveRecord(UserWallets::class, [
            'site_id' => self::SITE_ID, 'user_id' => $userId, 'currency' => self::CURRENCY, 'wallet_id' => self::uniqRuntimeId()
        ]);

        MockServer::with(function () use ($user, $employee) {
            $form = $this->container()->get(WebBonusForm::class);
            self::consoleOutputToDebug(static function () use ($form, $user, $employee) {
                $form->hasUserInForm = false;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException(['siteId' => self::SITE_ID, 'remoteBonusType' => self::REMOTE_BONUS]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException(self::moneyBonusFormSettings((string)$user->user_id, '200'));
                $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);
            });
        }, [
            [
                $this->bonusListRequest(self::SITE_ID),
                $this->bonusListResponse(),
            ],
            [
                $this->sendAddMoneyBonusRequest($userId, '200.00', $employee->email, '351371bbc3158354e2913da77851d0c5'),
                $this->bonusSuccessResponse(),
            ]
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $user->user_id], [
            'sum' => '200.00',
            'currency' => self::CURRENCY,
            'type_id' => BonusLog::TYPE_MANUAL_BONUS,
            'bonus' => self::REMOTE_BONUS,
            'bonus_type' => BonusTypes::TYPE_MONEY,
            'admin_comment' => '9_test',
            'operator_id' => $employee->employee_id,
            'extra' => [
                'adminComment' => '9_test',
                'currency' => self::CURRENCY,
                'ignoreProductMaxTransfer' => false,
                'sum' => '200.00',
                'userId' => '1',
                'wager' => 2,
                'wagerHours' => 960,
            ],
        ]);
    }

    public function testMultiAssign(): void
    {
        $this->container()->get(CacheInterface::class)->clear();

        //need static email because self::uniqRuntimeId() change $signature in $this->sendAddMoneyBonusRequest
        $employee = $this->haveEmployee(['email' => '<EMAIL>']);
        $this->haveRates();

        [$userId1Ok, $userId2Ok, $userId3Bad]  = [1, 2, 3];
        $this->haveUserRecord(['user_id' => $userId1Ok, 'site_id' => self::SITE_ID, 'currency' => self::CURRENCY]);
        $this->haveUserRecord(['user_id' => $userId2Ok, 'site_id' => self::SITE_ID, 'currency' => self::CURRENCY]);
        $this->haveUserRecord(['user_id' => $userId3Bad, 'site_id' => self::SITE_ID, 'currency' => self::CURRENCY]);

        $this->haveRecords(UserWallets::class, [
            ['site_id' => self::SITE_ID, 'user_id' => $userId1Ok, 'currency' => self::CURRENCY, 'wallet_id' => self::uniqRuntimeId()],
            ['site_id' => self::SITE_ID, 'user_id' => $userId2Ok, 'currency' => self::CURRENCY, 'wallet_id' => self::uniqRuntimeId()],
            ['site_id' => self::SITE_ID, 'user_id' => $userId3Bad, 'currency' => self::CURRENCY, 'wallet_id' => self::uniqRuntimeId()],
        ]);

        MockServer::with(function () use ($employee, $userId1Ok, $userId2Ok, $userId3Bad) {
            $form = $this->container()->get(WebBonusForm::class);

            self::consoleOutputToDebug(function () use ($form, $employee, $userId1Ok, $userId2Ok, $userId3Bad) {
                $multiAssignString = "$userId1Ok 100.00\n $userId2Ok 200.00\n $userId3Bad 300.00";
                $form->hasUserInForm = false;
                $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
                $form->validateOrException(['siteId' => self::SITE_ID, 'remoteBonusType' => self::REMOTE_BONUS]);

                $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
                $form->validateOrException(self::moneyBonusFormSettings($multiAssignString, null));

                $sendError = $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $employee, null);
                $expected = $userId3Bad . ': {
    "message": "Remote validation error (480): Unknown validation error"
}';
                $this->assertSame($expected, $sendError);

                $this->assertSame("$userId3Bad 300.00", $form->bonusForm->userId);
            });
        }, [
            [
                $this->bonusListRequest(self::SITE_ID),
                $this->bonusListResponse(),
            ],
            [
                $this->sendAddMoneyBonusRequest($userId1Ok, '100.00', $employee->email, '75b7d38b68bed6c24f9d8a4d6ea4b216'),
                $this->bonusSuccessResponse(),
            ],
            [
                $this->sendAddMoneyBonusRequest($userId2Ok, '200.00', $employee->email, '573139e8a72fd57e29f9e2b5c105e583'),
                $this->bonusSuccessResponse(),
            ],
            [
                $this->sendAddMoneyBonusRequest($userId3Bad, '300.00', $employee->email, '324a1f9daafc339fa955bb4cd1ec7197'),
                $this->bonusValidationErrorResponse(),
            ],
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId1Ok], [
            'sum' => '100.00',
            'currency' => self::CURRENCY,
        ]);

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId2Ok], [
            'sum' => '200.00',
            'currency' => self::CURRENCY,
        ]);

        $this->dontSeeRecord(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => $userId3Bad]);
    }
}
