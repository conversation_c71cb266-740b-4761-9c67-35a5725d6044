<?php

declare(strict_types=1);

namespace app\tests\functional\reports;

use app\back\components\helpers\DateHelper;
use app\back\entities\Country;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\reports\Income\IncomeConfig;
use app\back\repositories\Users;

class IncomeReportTest extends BaseReportTestCase
{
    public function testLocations(): void
    {
        $this->haveRates();

        $siteIdCIS = Site::MS;
        $siteIdInt = Site::GGB;
        $siteIdUA = Site::V777;
        $siteIdUALIC = Site::GGUA;

        for ($i = 1; $i <= 13; $i++) {
            ${"userid$i"} = self::uniqRuntimeId();
        }

        $this->haveRecords(Users::class, [
            //CIS
            ['site_id' => $siteIdCIS, 'user_id' => $userid1, 'country' => Country::RU,],
            ['site_id' => $siteIdInt, 'user_id' => $userid2, 'country' => Country::RU,],
            ['site_id' => $siteIdCIS, 'user_id' => $userid3, 'country' => Country::UA,],
            //UA
            ['site_id' => $siteIdUA, 'user_id' => $userid4, 'country' => Country::UA,],
            ['site_id' => $siteIdInt, 'user_id' => $userid5, 'country' => Country::UA,],
            ['site_id' => $siteIdUA, 'user_id' => $userid6, 'country' => Country::DE,],
            //UALIC
            ['site_id' => $siteIdUALIC, 'user_id' => $userid7, 'country' => Country::DE,],
            //DE
            ['site_id' => $siteIdInt, 'user_id' => $userid8, 'country' => Country::DE,],
            //PL
            ['site_id' => $siteIdInt, 'user_id' => $userid9, 'country' => Country::PL,],
            //LATAM
            ['site_id' => $siteIdInt, 'user_id' => $userid10, 'country' => Country::CL,],
            //TR
            ['site_id' => $siteIdInt, 'user_id' => $userid11, 'country' => Country::TR,],
            //BR
            ['site_id' => $siteIdInt, 'user_id' => $userid12, 'country' => Country::BR,],
            //Int
            ['site_id' => $siteIdInt, 'user_id' => $userid13, 'country' => Country::NL,],
        ]);

        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid2, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdCIS, $userid3, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, '2023-09-09', '120.00');

        $this->haveUserStatRecordCustom($siteIdUA, $userid4, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid5, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdUA, $userid6, '2023-09-02', '120.00');
        $this->haveUserStatRecordCustom($siteIdUA, $userid4, '2023-09-09', '120.00');

        $this->haveUserStatRecordCustom($siteIdUALIC, $userid7, '2023-09-03', '120.00');

        $this->haveUserStatRecordCustom($siteIdInt, $userid8, '2023-09-03', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid9, '2023-09-03', '120.00');

        $this->haveUserStatRecordCustom($siteIdInt, $userid10, '2023-09-08', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid11, '2023-09-08', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid12, '2023-09-08', '120.00');
        $this->haveUserStatRecordCustom($siteIdInt, $userid13, '2023-09-08', '120.00');

        $config = $this->reportConfig(IncomeConfig::class);
        $config->loadAndValidateOrException([
            'metrics' => [IncomeConfig::METRIC_IN],
            'isTotals' => true,
            'groups' => ['day'],
            'isHtmlVersion' => true,
            'filters' => [
                ['columns_config',
                    'CIS: l_CIS
                    UA: l_UA, l_UALIC
                    DE: l_DE
                    PL: l_PL
                    LATAM: l_LATAM
                    INT: l_INT'],
                ['currency', CurrencyColumn::EUR],
                ['date_range', DateHelper::range('2023-09-02', '2023-09-09')],
                ['diff_weeks', '1,2,3'],
                ['diff_threshold', 10],
            ]
        ]);

        ['data' => $data] = $config->dataAndColumns();

        self::assertEquals([
            [
                'day' => '2023-09-09',
                'm_1' => '120',
                'm_2' => '120',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => '120.0000000000000000',
                'prev_m_2' => '120.0000000000000000',
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
                '__title__m_1' => '120',
                '__title__m_2' => '120'

            ], [
                'day' => '2023-09-08',
                'm_1' => '',
                'm_2' => '',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '240',
                'm_6' => '120',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => 'green'],
            ], [
                'day' => '2023-09-07',
                'm_1' => '',
                'm_2' => '',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ], [
                'day' => '2023-09-06',
                'm_1' => '',
                'm_2' => '',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ], [
                'day' => '2023-09-05',
                'm_1' => '',
                'm_2' => '',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ], [
                'day' => '2023-09-04',
                'm_1' => '',
                'm_2' => '',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ], [
                'day' => '2023-09-03',
                'm_1' => '',
                'm_2' => '120',
                'm_3' => '120',
                'm_4' => '120',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ], [
                'day' => '2023-09-02',
                'm_1' => '360',
                'm_2' => '360',
                'm_3' => '',
                'm_4' => '',
                'm_5' => '',
                'm_6' => '',
                'prev_m_1' => null,
                'prev_m_2' => null,
                'prev_m_3' => null,
                'prev_m_4' => null,
                'prev_m_5' => null,
                'prev_m_6' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_2' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__style__m_3' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_4' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_5' => ['white-space' => 'nowrap', 'color' => ''],
                '__style__m_6' => ['white-space' => 'nowrap', 'color' => ''],
            ],
        ], $data);
    }


    public function testWeeksInLocations(): void
    {
        $this->haveRates();

        $siteIdCIS = Site::MS;
        $userid1 = self::uniqRuntimeId();

        $this->haveRecord(Users::class, ['site_id' => $siteIdCIS, 'user_id' => $userid1, 'country' => Country::RU,]);

        $dateTest = '2023-09-02';

        $config = $this->createLoadConfig($dateTest);

        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, $dateTest, '30.00');
        for ($week = 1; $week <= 3; $week++) {
            $this->haveUserStatRecordCustom($siteIdCIS, $userid1, date('Y-m-d', strtotime("-$week week", strtotime($dateTest))), '20.00');
        }

        ['data' => $data] = $config->dataAndColumns();

        self::assertEquals([
            [
                'day' => '2023-09-02',
                'm_1' => '30',
                'prev_m_1' => null,
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => 'green'],
            ]
        ], $data);

        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, date('Y-m-d', strtotime("-4 week", strtotime($dateTest))), '30.00');

        $config = $this->createLoadConfig($dateTest);

        ['data' => $data] = $config->dataAndColumns();

        self::assertEquals([
            [
                'day' => '2023-09-02',
                'm_1' => '30',
                'prev_m_1' => '15.0000000000000000',
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => 'green'],
                '__title__m_1' => '15'
            ]
        ], $data);

        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, date('Y-m-d', strtotime("-5 week", strtotime($dateTest))), '50.00');

        $config = $this->createLoadConfig($dateTest);

        ['data' => $data] = $config->dataAndColumns();

        self::assertEquals([
            [
                'day' => '2023-09-02',
                'm_1' => '30',
                'prev_m_1' => '40.0000000000000000',
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => 'red'],
                '__title__m_1' => '40'
            ]
        ], $data);

        $this->haveUserStatRecordCustom($siteIdCIS, $userid1, date('Y-m-d', strtotime("-6 week", strtotime($dateTest))), '120.00');

        $config = $this->createLoadConfig($dateTest);
        ['data' => $data] = $config->dataAndColumns();

        self::assertEquals([
            [
                'day' => '2023-09-02',
                'm_1' => '30',
                'prev_m_1' => '40.0000000000000000',
                '__style__m_1' => ['white-space' => 'nowrap', 'color' => 'red'],
                '__title__m_1' => '40'
            ]
        ], $data);
    }

    private function haveUserStatRecordCustom(int $siteId, int $userId, string $date, string $sum): void
    {
        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'amount_orig' => $sum,
            'currency' => CurrencyColumn::EUR,
            'status' => UserTransaction::STATUS_SUCCESS,
            'dir' => UserTransaction::DIR_IN,
            'ext_type' => 1,
            'updated_at' => new \DateTimeImmutable($date),
        ]);
    }

    private function createLoadConfig(string $dateTest): BaseReportConfig
    {
        $config = $this->reportConfig(IncomeConfig::class);
        $config->loadAndValidateOrException([
            'metrics' => [IncomeConfig::METRIC_IN],
            'isTotals' => true,
            'groups' => ['day'],
            'isHtmlVersion' => true,
            'filters' => [
                ['columns_config', 'CIS: l_CIS'],
                ['currency', CurrencyColumn::EUR],
                ['date_range', DateHelper::range($dateTest, $dateTest)],
                ['diff_weeks', '4,5'],
                ['diff_threshold', 10],
            ]
        ]);

        return $config;
    }
}
