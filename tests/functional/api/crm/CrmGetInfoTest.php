<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\Host;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserSpecialInfo;
use app\back\entities\UserWallet;
use app\back\repositories\Hosts;
use app\back\repositories\UserLogins;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserWallets;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;

/** @see InfoMethod */
class CrmGetInfoTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testOneUserWithoutCurrency(): void
    {
        $userName = 'John Doe';
        $email = '<EMAIL>';
        $date = '2020-01-02 09:46:17';
        $loginLastAt = '2017-01-09 11:12:39';
        $depFirstAt = '2017-01-10 11:12:39';
        $depLastAt = '2017-01-20 11:12:39';
        $depUsd = '10.00';
        $depRub = '700.00';
        $depCont = 1;
        $depMedianEur = '300.00';
        $depMedianOrig = '0';

        $refcode = $this->haveRefcodeRecord([]);
        $user = $this->haveUserRecord([
            'name' => $userName,
            'email' => $email,
            'is_blocked' => false,
            'is_toxic' => false,
            'date' => new \DateTimeImmutable($date),
            'status' => User::STATUS_VIP,
            'refcode_id' => $refcode->id
        ]);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'login_last_at' => new \DateTimeImmutable($loginLastAt),
            'dep_first_at' => new \DateTimeImmutable($depFirstAt),
            'dep_last_at' => new \DateTimeImmutable($depLastAt),
            'dep_lt_usd' => $depUsd,
            'dep_lt_rub' => $depRub,
            'dep_lt_count' => $depCont,
            'dep_median_eur' => $depMedianEur,
            'gender' => 'M',
        ]);

        $this->sendAPI('crm', 'get/info', [
            'site_id' => ['=' => $user->site_id],
            'user_id' => ['=' => $user->user_id],
        ]);

        $this->seeRowsInCsv([
            [
                'site_id' => $user->site_id,
                'user_id' => $user->user_id,
                'name' => $userName,
                'email' => $email,
                'is_blocked' => false,
                'is_toxic' => false,

                'registered_at' => $date,
                'last_login_at' => $loginLastAt,
                'first_dep_at' => $depFirstAt,
                'last_dep_at' => $depLastAt,

                'in_sum_usd' => $depUsd,
                'in_sum_rub' => $depRub,
                'in_count' => $depCont,
                'in_median_sum_eur' => $depMedianEur,
                'in_median_sum_orig' => $depMedianOrig,

                'out_sum_rub' => '0.00',
                'out_sum_usd' => '0.00',
                'out_count' => 0,

                'inout_sum_rub' => $depRub,
                'inout_sum_usd' => $depUsd,

                'refcode' => $refcode->code,
                'status' => User::STATUS_VIP,
                'active_status' => User::ACTIVE_STATUS_ACTIVE,
                'is_rm' => false,
                'email_open_time' => UserSpecialInfo::DEFAULT_LETTER_OPEN_HOUR,
            ],
        ]);
    }

    public function testUserWithCurrencyAndWithHostLogin(): void
    {
        $depMedianEur = '300.00';
        $currency = 'EUR';
        $domainLastLogin = 'domainlogin.com';

        $this->haveRates();

        $user = $this->haveUserRecord();

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'dep_median_eur' => $depMedianEur,
        ]);
        $this->haveRecord(UserWallets::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'wallet_id' => self::uniqRuntimeId(),
            'type' => UserWallet::TYPE_REAL,
            'currency' => $currency
        ]);

        /** @var Host $host1 */
        $host1 = $this->haveRecord(Hosts::class, ['host' => 'google.com']);
        /** @var Host $host2 */
        $host2 = $this->haveRecord(Hosts::class, ['host' => $domainLastLogin]);
        $this->haveRecord(UserLogins::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'login_id' => uniqid('', true),
            'host_id' => $host1->id,
            'date' => $user->date->sub(new \DateInterval('PT10S')),
        ]);
        $this->haveRecord(UserLogins::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'login_id' => uniqid('', true),
            'host_id' => $host2->id,
            'date' => $user->date->sub(new \DateInterval('PT1S')),
        ]);

        $this->sendAPI('crm', 'get/info', [
            'site_id' => ['=' => $user->site_id],
            'user_id' => ['=' => $user->user_id],
        ]);

        $this->seeRowsInCsv([
            [
                'site_id' => $user->site_id,
                'user_id' => $user->user_id,
                'in_median_sum_eur' => $depMedianEur,
                'currency' => $currency,
                'host' => $domainLastLogin,
            ],
        ]);
    }

    public function testLoginTokenPatch(): void
    {
        $user = $this->haveUserRecord(['site_id' => self::uniqSiteId([Site::PLATFORM_SITES_GI])]);

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'login_token' => '07e70c15-0f38-8bfc-ba65-f0ec85dc2812',
        ]);

        $this->sendAPI('crm', 'get/info', [
            'site_id' => ['=' => $user->site_id],
            'user_id' => ['=' => $user->user_id],
        ]);

        $this->seeRowsInCsv([
            [
                'site_id' => $user->site_id,
                'user_id' => $user->user_id,
                'login_token' => '08e70c15-0f38-8bfc-ba65-f0ec85dc2812',
            ],
        ]);
    }
}
