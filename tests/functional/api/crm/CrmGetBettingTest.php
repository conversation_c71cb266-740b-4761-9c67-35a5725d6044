<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\BettingEvent;
use app\back\entities\BettingOdd;
use app\back\modules\api\clients\crm\filter\BettingMethod;
use app\back\repositories\BettingEvents;
use app\back\repositories\BettingOdds;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use app\tests\libs\FakerUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(BettingMethod::class)]
class CrmGetBettingTest extends TestCase
{
    use FakerCheckUnitTrait;
    use FakerUnitTrait;
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testGetByWinBetPercent(): void
    {
        $date = date('Y-m-d H:i:s');
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveRates();

        $bettingBet1 = $this->haveBettingBetRecord(['user_id' => $userId1, 'site_id' => $siteId]);
        $bettingBet2 = $this->haveBettingBetRecord(['user_id' => $userId1, 'site_id' => $siteId]);
        $bettingBet3 = $this->haveBettingBetRecord(['user_id' => $userId1, 'site_id' => $siteId]);
        $bettingBet4 = $this->haveBettingBetRecord(['user_id' => $userId2, 'site_id' => $siteId]);
        $bettingBet5 = $this->haveBettingBetRecord(['user_id' => $userId2, 'site_id' => $siteId]);

        $eventId = self::uniqRuntimeId();

        $this->haveBettingOddsRecord(['id' => $bettingBet1->id, 'event_id' => $eventId, 'status' => BettingOdd::STATUS_WIN]);
        $this->haveBettingOddsRecord(['id' => $bettingBet2->id, 'event_id' => $eventId, 'status' => BettingOdd::STATUS_LOSS]);
        $this->haveBettingOddsRecord(['id' => $bettingBet3->id, 'event_id' => $eventId, 'status' => BettingOdd::STATUS_WIN]);
        $this->haveBettingOddsRecord(['id' => $bettingBet4->id, 'event_id' => $eventId, 'status' => BettingOdd::STATUS_WIN]);
        $this->haveBettingOddsRecord(['id' => $bettingBet5->id, 'event_id' => $eventId, 'status' => BettingOdd::STATUS_LOSS]);

        $bettingEvents = $this->haveRecord(BettingEvents::class, [
            'id' => $eventId,
            'status' => BettingEvent::STATUS_ENDED,
            'sport_id' => self::uniqRuntimeId(),
            'tournament_id' => self::uniqRuntimeId(),
            'type' => BettingEvent::TYPE_MATCH,
            'start_at' => $date,
            'updated_at' => $date,
        ]);

        $this->haveBettingTournamentRecord(['date' => $date, 'id' => $bettingEvents->tournament_id]);

        $this->sendAPI('crm', 'get/betting', ['site_id' => ['=' => $siteId], 'bet_win_percent' => ['>' => 40]]);
        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([['user_id' => $userId1],['user_id' => $userId2]]);

        $this->sendAPI('crm', 'get/betting', ['site_id' => ['=' => $siteId], 'bet_win_percent' => ['<' => 60]]);
        $this->seeResponseIsCsv();
        $this->seeRowsInCsv([['user_id' => $userId2]]);
        $this->seeRowsNotInCsv([['user_id' => $userId1]]);

        $this->sendAPI('crm', 'get/betting', ['site_id' => ['=' => $siteId], 'bet_win_percent' => ['>' => 70]]);
        $this->seeResponseEquals('');
    }

    private function haveBettingOddsRecord($props = []): void
    {
        $this->haveRecord(BettingOdds::class, [
                'bet_id' => $props['id'],
                'event_id' => $props['event_id'],
                'status' => $props['status'],BettingOdd::STATUS_WIN,
                'odd_id' => self::uniqRuntimeId(),
                'market_id' => self::uniqRuntimeId(),
            ]);
    }
}
