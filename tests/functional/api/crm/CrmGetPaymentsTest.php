<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\Rate;
use app\back\entities\UserTransaction;
use app\back\repositories\UserSpecialInfos;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use PHPUnit\Framework\TestCase;

class CrmGetPaymentsTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    private const int MIN_ELIGIBLE_SUM = 150;

    public function testUsers(): void
    {
        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();

        $this->haveRecord(UserSpecialInfos::class, [
            'site_id' => $siteId,
            'user_id' => $userId,
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'user_id' => ['=' => $userId],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $userId],
        ]);
    }

    public function testDepSumLt(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveRecords(UserSpecialInfos::class, [
            [
                'site_id' => $siteId,
                'user_id' => $userId1,
                'dep_lt_usd' => $this->notEligibleSum(),
            ],
            [
                'site_id' => $siteId,
                'user_id' => $userId2,
                'dep_lt_usd' => $this->eligibleSum(),
            ]
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'in_sum' => ['>=' => self::MIN_ELIGIBLE_SUM],
        ]);

        $this->seeResponseIsCsv();

        $this->seeRowsInCsv([
            ['user_id' => $userId2],
        ]);

        $this->seeRowsNotInCsv([
            ['user_id' => $userId1],
        ]);
    }

    public function testExclude(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->haveRecords(UserSpecialInfos::class, [
            [
                'site_id' => $siteId,
                'user_id' => $userId1,
                'dep_lt_usd' => $this->notEligibleSum(),
            ],
            [
                'site_id' => $siteId,
                'user_id' => $userId2,
                'dep_lt_usd' => $this->eligibleSum(),
            ]
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'user_id' => ['IN' => [$userId1, $userId2]],
            'exclude' => ['=' => 1],
            'currency' => ['=' => 'USD'],
            'in_sum' => ['>=' => self::MIN_ELIGIBLE_SUM],
        ]);

        $this->seeResponseIsCsv();

        $this->seeRowsInCsv([
            ['user_id' => $userId1],
        ]);

        $this->seeRowsNotInCsv([
            ['user_id' => $userId2],
        ]);
    }

    public function testAnyInSum(): void
    {
        $this->haveRates();

        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->makeDep($siteId, $userId1, $this->notEligibleSum());
        $this->makeDep($siteId, $userId2, $this->notEligibleSum());

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'any_in_sum' => ['>=' => self::MIN_ELIGIBLE_SUM],
        ]);

        $this->seeRowsNotInCsv([
            ['user_id' => $userId1],
            ['user_id' => $userId2],
        ]);

        $this->makeDep($siteId, $userId1, $this->eligibleSum());
        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'any_in_sum' => ['>=' => self::MIN_ELIGIBLE_SUM],
        ]);

        $this->seeRowsInCsv([
            ['user_id' => $userId1],
        ]);

        $this->makeWd($siteId, $userId2, $this->eligibleSum());
        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => $siteId],
            'any_out_sum' => ['>=' => self::MIN_ELIGIBLE_SUM],
        ]);

        $this->seeRowsInCsv([
            ['user_id' => $userId2],
        ]);
    }

    public function testValidateIncorrectArrayList(): void
    {
        $siteId = self::uniqSiteId();
        $userId1 = self::uniqRuntimeId();
        $userId2 = self::uniqRuntimeId();

        $this->sendAPI(
            'crm',
            'get/payments',
            [
                'site_id' => ['=' => $siteId],
                'user_id' => ['IN' => ['key1' => $userId1, 'key2' => $userId2]]
            ],
            422
        );
        $this->seeResponseContains('Incorrect user_id value, array must be list');

        $this->sendAPI(
            'crm',
            'get/payments',
            [
                'site_id' => ['=' => $siteId],
                'user_id' => ['IN' => [3 => $userId1, 5 => $userId2]]
            ],
            422
        );
        $this->seeResponseContains('Incorrect user_id value, array must be list');
    }

    private function makeDep(int $siteId, int $userId, string $amount): void
    {
        $this->makeTransaction($siteId, $userId, $amount, UserTransaction::DIR_IN);
    }

    private function makeWd(int $siteId, int $userId, string $amount): void
    {
        $this->makeTransaction($siteId, $userId, $amount, UserTransaction::DIR_OUT);
    }

    private function makeTransaction(int $siteId, int $userId, string $amount, int $dir): void
    {
        $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'dir' => $dir,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'status' => UserTransaction::STATUS_SUCCESS,
            'amount_orig' => $amount,
            'currency' => Rate::USD,
        ]);
    }

    private function eligibleSum(): string
    {
        return self::randomMoney(self::MIN_ELIGIBLE_SUM, self::MIN_ELIGIBLE_SUM * 2);
    }

    private function notEligibleSum(): string
    {
        return self::randomMoney(1, self::MIN_ELIGIBLE_SUM - 1);
    }
}
