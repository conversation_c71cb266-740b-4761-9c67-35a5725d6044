<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\modules\api\clients\crm\filter\PaymentsMethod;
use app\back\modules\api\components\Operators;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(PaymentsMethod::class)]
class CrmGetPaymentsActionTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testOutRate(): void
    {
        $userId = self::uniqRuntimeId();
        $this->haveRates();
        $usersStat = $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'dir' => UserTransaction::DIR_IN,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'status' => UserTransaction::STATUS_SUCCESS,
            'currency' => Rate::USD,
            'amount_orig' => '100',
        ]);
        $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => $userId,
            'created_at' => new \DateTimeImmutable('-15 minutes'),
            'dir' => UserTransaction::DIR_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'status' => UserTransaction::STATUS_SUCCESS,
            'currency' => Rate::USD,
            'amount_orig' => '109',
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => Site::CV],
            'payed_at' => ['>=' => date('Y-m-d H:i:s', strtotime('-1 hour'))],
            'out_rate' => ['>=' => 109]
        ]);

        $this->seeResponseIsCsv();
        $this->seeResponseEqualsCsvRows([['user_id' => $usersStat->user_id]]);
    }

    public function testOutRateNoWithdrawal(): void
    {
        $this->haveRates();

        $usersStat = $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'dir' => UserTransaction::DIR_IN,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'status' => UserTransaction::STATUS_SUCCESS,
            'amount_orig' => '100',
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => Site::CV],
            'payed_at' => ['>=' => date('Y-m-d H:i:s', strtotime('-1 hour'))],
            'out_rate' => ['<=' => 1]
        ]);

        $this->seeResponseIsCsv();
        $this->seeResponseEqualsCsvRows([['user_id' => $usersStat->user_id]]);
    }

    public function testOutRateNoIn(): void
    {
        $this->haveRates();

        $user = $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => self::uniqRuntimeId(),
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'dir' => UserTransaction::DIR_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'status' => UserTransaction::STATUS_SUCCESS,
            'amount_orig' => '100',
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => ['=' => Site::CV],
            'payed_at' => ['>=' => date('Y-m-d H:i:s', strtotime('-1 hour'))],
            'out_rate' => ['<' => 1]
        ]);

        $this->seeRowsNotInCsv([
            ['user_id' => $user->user_id],
        ]);
    }

    public function testStatus(): void
    {
        $this->haveRates();

        $usersStat = $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => self::uniqRuntimeId(),
            'dir' => UserTransaction::DIR_OUT,
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'status' => UserTransaction::STATUS_NEW,
        ]);

        $usersStat2 = $this->haveUserTransactionRecord([
            'site_id' => Site::CV,
            'user_id' => self::uniqRuntimeId(),
            'dir' => UserTransaction::DIR_OUT,
            'created_at' => new \DateTimeImmutable('-5 minutes'),
            'status' => UserTransaction::STATUS_SUCCESS,
        ]);

        $this->sendAPI('crm', 'get/payments', [
            'site_id' => [Operators::EQ => Site::CV],
            'status' => [Operators::IN => [UserTransaction::STATUS_NEW, UserTransaction::STATUS_PENDING]],
            'payed_at' => [Operators::GE => date('Y-m-d H:i:s', strtotime('-1 hour'))],
        ]);

        $this->seeResponseIsCsv();
        $this->seeResponseEqualsCsvRows([['user_id' => $usersStat->user_id]]);
        $this->seeRowsNotInCsv([['user_id' => $usersStat2->user_id]]);
    }
}
