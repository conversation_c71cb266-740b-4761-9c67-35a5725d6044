<?php

declare(strict_types=1);

namespace app\tests\functional\api\yhelper;

use app\back\entities\Site;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\modules\api\clients\yhelper\PutWithdrawalApproveMethod;
use app\back\repositories\UserTransactions;
use app\back\repositories\Withdrawals;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(PutWithdrawalApproveMethod::class)]
class YhelperPutWithdrawalApproveTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    /** set in back/config/env/test/api_clients.php */
    private int $allowedSiteId = Site::CV;

    public function testBasic(): void
    {
        $d3Ago = (new \DateTimeImmutable('-3 days'))->setTime(0, 0);
        $this->haveRates();
        $transactionId = (string)self::uniqRuntimeId();

        $this->haveUserTransactionRecord([
            'site_id' => $this->allowedSiteId,
            'transaction_id' => $transactionId,
            'op_id' => UserTransaction::OP_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'withdraw_approved_at' => null,
            'created_at' => $d3Ago,
        ]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $this->allowedSiteId,
            'invoice_id' => $transactionId,
        ]);

        /** @var UserTransaction $wd */
        $wd = $this->seeRecord(UserTransactions::class, ['site_id' => $this->allowedSiteId, 'transaction_id' =>  $transactionId]);
        $this->assertNotNull($wd->withdraw_approved_at);
    }

    public function testFailWithdrawApprovedNotFound(): void
    {
        $transactionId = self::uniqRuntimeId();

        $this->dontSeeRecord(UserTransactions::class, ['site_id' => $this->allowedSiteId, 'transaction_id' =>  $transactionId]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $this->allowedSiteId,
            'invoice_id' => $transactionId,
        ], 422);

        $this->seeResponseContains('not found');
    }

    public function testWrongStatus(): void
    {
        $d3Ago = (new \DateTimeImmutable('-3 days'))->setTime(0, 0);
        $this->haveRates();
        $transactionId = self::uniqRuntimeId();

        $this->haveUserTransactionRecord([
            'site_id' => $this->allowedSiteId,
            'transaction_id' => (string)$transactionId,
            'op_id' => UserTransaction::OP_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_OUT,
            'status' => UserTransaction::STATUS_SUCCESS,
            'created_at' => $d3Ago,
        ]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $this->allowedSiteId,
            'invoice_id' => $transactionId,
        ], 422);

        $this->seeResponseContains('wrong status');
    }

    public function testWrongType(): void
    {
        $d3Ago = (new \DateTimeImmutable('-3 days'))->setTime(0, 0);
        $this->haveRates();
        $transactionId = self::uniqRuntimeId();

        $this->haveUserTransactionRecord([
            'site_id' => $this->allowedSiteId,
            'transaction_id' => (string)$transactionId,
            'op_id' => UserTransaction::OP_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_IN,
            'status' => UserTransaction::STATUS_NEW,
            'created_at' => $d3Ago,
        ]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $this->allowedSiteId,
            'invoice_id' => $transactionId,
        ], 422);

        $this->seeResponseContains('invalid type or dir');
    }

    public function testWithdrawSynced(): void
    {
        $d3Ago = (new \DateTimeImmutable('-3 days'))->setTime(0, 0);
        $this->haveRates();
        $transactionId = self::uniqRuntimeId();

        $this->haveUserTransactionRecord([
            'site_id' => $this->allowedSiteId,
            'transaction_id' => (string)$transactionId,
            'op_id' => UserTransaction::OP_OUT,
            'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
            'dir' => UserTransaction::DIR_OUT,
            'status' => UserTransaction::STATUS_NEW,
            'created_at' => $d3Ago,
        ]);

        $this->haveRecord(Withdrawals::class, [
            'site_id' => $this->allowedSiteId,
            'transaction_id' => (string)$transactionId,
            'status' => Withdrawal::STATUS_SYNCED,
            'decision' => Withdrawal::DECISION_DENY,
            'operator_id' => self::uniqRuntimeId(),
        ]);

        $this->sendAPI('yhelper', 'put/withdrawal-approve', [
            'site_id' => $this->allowedSiteId,
            'invoice_id' => $transactionId,
        ], 422);

        $this->seeResponseContains('is already processed');
    }
}
