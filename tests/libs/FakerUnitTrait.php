<?php

declare(strict_types=1);

namespace app\tests\libs;

use app\back\components\helpers\Arr;
use app\back\components\helpers\UuidHelper;
use app\back\entities\BettingTournament;
use app\back\entities\BonusJournal;
use app\back\entities\Contact;
use app\back\entities\Country;
use app\back\entities\Employee;
use app\back\entities\Game;
use app\back\entities\Operator;
use app\back\entities\Rate;
use app\back\entities\Refcode;
use app\back\entities\S2pOrder;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserGameToken;
use app\back\entities\UserKyc;
use app\back\entities\UserLogin;
use app\back\entities\UserSpecialInfo;
use app\back\entities\UserTransaction;
use app\back\entities\UserStatusNormalThreshold;
use app\back\entities\UserStatusVipThreshold;
use app\back\entities\WpAffOwnerGroup;
use app\back\entities\WpWebmaster;
use app\back\modules\checks\checks\rules\BaseRule;
use app\back\modules\task\TaskConfigs;
use app\back\repositories\AuthAssignments;
use app\back\repositories\BettingTournaments;
use app\back\repositories\BonusJournals;
use app\back\repositories\Contacts;
use app\back\repositories\Employees;
use app\back\repositories\Games;
use app\back\repositories\Operators;
use app\back\repositories\Rates;
use app\back\repositories\Refcodes;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pProjects;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\UserGameTokens;
use app\back\repositories\UserKycs;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserStatusNormalThresholds;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserStatusVipThresholds;
use app\back\repositories\WpAffOwnerGroups;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

trait FakerUnitTrait
{
    protected function uniqTasksQueueName(): string
    {
        static $queues;
        static $lastQueueKey = 0;
        if (!isset($queues)) {
            $queues = array_keys($this->container()->get(TaskConfigs::class)->getTasksQueues());
        }

        $queue = $queues[$lastQueueKey];
        $lastQueueKey++;
        $lastQueueKey %= count($queues);
        return $queue;
    }

    protected function haveBonusJournalRecord(array $props = []): BonusJournal
    {
        $props['site_id'] ??= self::uniqSiteId();
        $props['user_id'] ??= self::uniqRuntimeId();
        $props['template_id'] ??= self::uniqRuntimeUuid();
        $props['journal_id'] ??= self::uniqRuntimeUuid();
        $props['trigger_id'] ??= self::uniqRuntimeUuid();
        $props['created_at'] ??= new \DateTimeImmutable();
        $props['action_type'] ??= BonusJournal::ACTION_GRANT_REWARD;
        $journal = $this->haveRecord(BonusJournals::class, $props);
        self::assertInstanceOf(BonusJournal::class, $journal);
        return $journal;
    }

    protected function haveContactRecord(array $props = []): Contact
    {
        $props['recipients'] ??= '<EMAIL>, <EMAIL>';
        $props['type'] ??= Contact::TYPE_EMAIL;
        $contact = $this->haveRecord(Contacts::class, $props);
        self::assertInstanceOf(Contact::class, $contact);
        return $contact;
    }

    protected function haveWpWebmasterRecord(array $props = []): WpWebmaster
    {
        $props['id'] ??= self::uniqRuntimeId();
        $props['registered_at'] ??= new \DateTimeImmutable();
        $webmaster = $this->haveRecord(WpWebmasters::class, $props);
        self::assertInstanceOf(WpWebmaster::class, $webmaster);
        return $webmaster;
    }

    protected function haveWpAffOwnerGroupRecord(array $props): WpAffOwnerGroup
    {
        $affOwnerGroup = $this->haveRecord(WpAffOwnerGroups::class, $props);
        self::assertInstanceOf(WpAffOwnerGroup::class, $affOwnerGroup);
        return $affOwnerGroup;
    }

    protected function haveRefcodeRecord(array $props): Refcode
    {
        $props['id'] ??= self::uniqRuntimeId();
        $props['code'] ??= self::uniqRuntimeUuid();
        $affOwnerGroup = $this->haveRecord(Refcodes::class, $props);
        self::assertInstanceOf(Refcode::class, $affOwnerGroup);
        return $affOwnerGroup;
    }

    protected function haveUserTransactionRecord(array $props): UserTransaction
    {
        if (array_key_exists('date', $props)) {
            throw new \InvalidArgumentException('Use created_at instead of date');
        }

        if (array_key_exists('date_update', $props)) {
            throw new \InvalidArgumentException('Use updated_at instead of date_update');
        }

        $dateRate ??= $props['created_at'] ?? new \DateTimeImmutable();
        if ($dateRate instanceof \DateTimeImmutable) {
            $dateRate = $dateRate->format('Y-m-d H:i:s');
        }
        $currency = $props['currency'] ?? self::randomCurrency();
        $amountOrig = $props['amount_orig'] ?? self::randomMoney();

        $props['site_id'] ??= self::uniqSiteId();
        $props['user_id'] ??= self::uniqRuntimeId();
        $props['transaction_id'] ??= (string) self::uniqRuntimeId();
        $props['status'] ??= UserTransaction::STATUS_SUCCESS;
        $props['balance_type'] ??= UserTransaction::BALANCE_TYPE_REAL;
        $props['ext_type'] ??= UserTransaction::EXT_TYPE_NORMAL;
        $props['amount_usd'] ??= (string) $this->ratesRepo()->convert($amountOrig, $currency, Rate::USD, $dateRate);
        $props['amount_rub'] ??= (string) $this->ratesRepo()->convert($amountOrig, $currency, Rate::RUB, $dateRate);
        $props['amount_eur'] ??= (string) $this->ratesRepo()->convert($amountOrig, $currency, Rate::EUR, $dateRate);
        $props['amount_orig'] = $amountOrig;
        $props['currency'] = (string) $currency;
        $props['op_id'] ??= self::randomItem(array_keys(UserTransaction::OPERATIONS));
        $props['status'] ??= self::randomItem(array_keys(UserTransaction::STATUSES));
        $props['created_at'] ??= $props['updated_at'] ?? new \DateTimeImmutable();
        $props['updated_at'] ??= $props['created_at'] ?? new \DateTimeImmutable();
        $props['comment'] ??= null;

        $stat = $this->haveRecord(UserTransactions::class, $props);
        self::assertInstanceOf(UserTransaction::class, $stat);
        return $stat;
    }

    protected function haveGame(string $name, ?int $vendorId = null): Game
    {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return $this->haveRecord(Games::class, array_filter(['name' => $name, 'vendor_id' => $vendorId]));
    }

    protected function haveUserGameTokenRecord(array $props): UserGameToken
    {
        $props['site_id'] ??= self::uniqSiteId();
        $props['user_id'] ??= self::uniqRuntimeId();
        $props['token_id'] ??= self::uniqRuntimeUuid();
        $props['currency'] ??= self::randomCurrency();
        $props['balance_type'] = UserGameToken::BALANCE_TYPE_REAL;
        $props['session_type'] = UserGameToken::SESSION_TYPE_PAIDSPINS;
        $props['created_at'] ??= $props['updated_at'] ?? new \DateTimeImmutable();
        $props['updated_at'] ??= $props['created_at'] ?? new \DateTimeImmutable();

        if (is_string($props['created_at'])) {
            $props['created_at'] = new \DateTimeImmutable($props['created_at']);
        }
        if (is_string($props['updated_at'])) {
            $props['updated_at'] = new \DateTimeImmutable($props['updated_at']);
        }

        $token = $this->haveRecord(UserGameTokens::class, $props);
        self::assertInstanceOf(UserGameToken::class, $token);
        return $token;
    }

    protected function haveUserRecord(array $props = []): User
    {
        $props['user_id'] ??= self::uniqRuntimeId();
        $props['site_id'] ??= self::uniqSiteId();
        $props['date'] ??= new \DateTimeImmutable();
        if (is_string($props['date'])) {
            $props['date'] = new \DateTimeImmutable($props['date']);
        }
        if (!array_key_exists('email', $props)) {
            $props['email'] = "user{$props['user_id']}@test.com";
        }

        $affOwnerGroup = $this->haveRecord(Users::class, $props);
        self::assertInstanceOf(User::class, $affOwnerGroup);
        return $affOwnerGroup;
    }

    /** @return User[] */
    protected function haveUserRecords(array $props): array
    {
        return array_map(fn($record) => $this->haveUserRecord($record), $props);
    }

    protected function haveOperatorRecord(array $props = []): Operator
    {
        $props['id'] ??= self::uniqRuntimeId();
        $props['email'] ??= "operator" . self::uniqRuntimeId() . "@test.test";
        $props['name'] ??= $props['email'];
        $props['status'] ??= true;
        $operator = $this->haveRecord(Operators::class, $props);
        self::assertInstanceOf(Operator::class, $operator);
        return $operator;
    }

    public function haveRates(): void
    {
        foreach (['RUB' => 70.0, 'USD' => 1.0, 'EUR' => 0.9, 'UAH' => 27.0] as $code => $rate) {
            $this->haveRecord(Rates::class, [
                'code' => $code,
                'rate' => (string) $rate,
                'date' => new \DateTimeImmutable('2000-01-01'),
            ]);
        }
    }

    protected function haveEmployee(array $attributes = []): Employee
    {
        $attributes['email'] ??= 'test' . self::uniqRuntimeId() . '@email.com';
        $attributes['status'] ??= Employee::STATUS_ACTIVE;
        $attributes['password'] ??= md5(random_bytes(32));
        $employee = $this->haveRecord(Employees::class, $attributes);
        self::assertInstanceOf(Employee::class, $employee);
        return $employee;
    }

    protected function haveAuthAssigment(int $employeeId, string $authItem): void
    {
        self::db()->createCommand()->insert(AuthAssignments::TABLE_NAME, [
            'employee_id' => $employeeId,
            'item_name' => $authItem,
        ])->execute();
    }

    protected function haveS2pOrderRecord(array $props = []): S2pOrder
    {
        $sumUsd = $props['summ'] ?? round((float)(random_int(0, 1000) . '.' . mt_rand()), 2);
        $dateRate = $props['date_created'] ?? new \DateTimeImmutable();
        $projectId = $props['project_id'] ?? self::randomItem($this->projectsWithSiteId());

        $props['id'] ??= uniqid("", false);
        $props['project_id'] ??= $projectId;
        $props['site_id'] = $props['site_id'] ?? $this->container()->get(S2pProjects::class)->getSiteIdByProjectId($projectId);
        $props['user_id'] = $props['user_id'] ?? self::uniqRuntimeId();
        $props['date_created'] ??= new \DateTimeImmutable();
        $props['date'] = $props['date'] ?? new \DateTimeImmutable();
        $props['summ'] = (string) $sumUsd;
        $props['summ_rub'] = (string) ($props['summ_rub'] ?? $this->ratesRepo()->convert($sumUsd, Rate::USD, Rate::RUB, $dateRate->format('Y-m-d H:i:s')));
        $props['summ_eur'] = (string) ($props['summ_eur'] ?? $this->ratesRepo()->convert($sumUsd, Rate::USD, Rate::EUR, $dateRate->format('Y-m-d H:i:s')));
        $props['ip'] = $props['ip'] ?? implode('.', array_map(static fn() => random_int(0, 255), range(0, 3)));
        $props['status'] = $props['status'] ?? self::randomItem(array_keys(S2pOrder::STATUSES));
        $props['type'] = $props['type'] ?? self::randomItem(array_keys(S2pOrder::TYPES));
        $props['requisite'] = $props['requisite'] ?? uniqid("", false);
        $props['pay_sys_id'] = $props['pay_sys_id'] ?? null;

        $s2pOrder = $this->haveRecord(S2pOrders::class, $props);
        self::assertInstanceOf(S2pOrder::class, $s2pOrder);
        return $s2pOrder;
    }

    protected function projectsWithSiteId(): array
    {
        return (new Query($this->db()))
            ->select(['id'], 'DISTINCT')
            ->from(S2pProjects::TABLE_NAME)
            ->where(['IS NOT', 'site_id', null])
            ->column();
    }

    protected function haveS2pPaySystems(int $count = 10): void
    {
        $current = (int) (new Query($this->db()))->from(S2pPaySystems::TABLE_NAME)->count();
        $count = max($count - $current, 0);
        if ($count === 0) {
            return;
        }
        $newPySys = array_map(static fn() => ['code' => uniqid("pay_sys_", false)], range(0, $count - 1));
        $this->db()->createCommand()->batchInsert(S2pPaySystems::TABLE_NAME, ['code'], $newPySys)->execute();
    }

    protected function haveBettingTournamentRecord(array $props = []): BettingTournament
    {
        $date = $props['date'] ? new \DateTimeImmutable($props['date']) : new \DateTimeImmutable();
        unset($props['date']);

        $props['id'] ??= (string) self::uniqRuntimeId();
        $props['name'] ??= (string) self::uniqRuntimeId();
        $props['sport_id'] ??= self::uniqRuntimeId();
        $props['country_code'] ??= 'NL';
        $props['start_at'] ??= $date;
        $props['end_at'] ??= $date;
        $props['updated_at'] ??= $date;
        $journal = $this->haveRecord(BettingTournaments::class, $props);
        self::assertInstanceOf(BettingTournament::class, $journal);
        return $journal;
    }

    protected function haveUserLogin(User $user, string $datetime, array $params = []): UserLogin
    {
        $params['success'] ??= true;
        /** @var UserLogin $login */
        $login = $this->haveRecord(UserLogins::class, [
            'site_id' => $user->site_id,
            'user_id' => $user->user_id,
            'login_id' => (string)self::uniqRuntimeId(),
            'date' => new \DateTimeImmutable($datetime),
            ...$params,
        ]);
        return $login;
    }

    protected function haveUserSpecialInfo(array $props = []): UserSpecialInfo
    {
        $props['site_id'] ??= self::uniqSiteId();
        $props['user_id'] ??= self::uniqRuntimeId();

        $usi = $this->haveRecord(UserSpecialInfos::class, $props);
        self::assertInstanceOf(UserSpecialInfo::class, $usi);
        return $usi;
    }

    protected function ruleForCheck(string $ruleClass, array $params = []): BaseRule
    {
        /** @var BaseRule $ruleClass */
        $ruleData = [
            "id" => $ruleClass::getClassId(),
            ...$params,
        ];
        /** @var BaseRule $rule */
        $rule = $this->container()->get($ruleClass);
        Arr::configure($rule, $ruleData);
        return $rule;
    }

    protected function haveUserStatusNormalThreshold(array $props = []): UserStatusNormalThreshold
    {
        $props['country'] ??= 'XX';
        $props['ngr_amount_active'] ??= 30;
        $props['ngr_amount_high'] ??= 112.5;
        $props['ngr_cloud_percent'] ??= 85;

        $usnt = $this->haveRecord(UserStatusNormalThresholds::class, $props);
        self::assertInstanceOf(UserStatusNormalThreshold::class, $usnt);
        return $usnt;
    }

    public function haveUserKycRecord(array $userKycAttributes, array $progressAttributes = []): UserKyc
    {
        $userKycAttributes['site_id'] ??= self::uniqSiteId();
        $userKycAttributes['user_id'] ??= self::uniqRuntimeId();
        $userKycAttributes['kyc_status'] ??= self::randomItem(UserKyc::KYC_STATUSES);
        $userKycAttributes['status_updated_at'] ??= new \DateTimeImmutable();

        if (!isset($userKycAttributes['last_progress_id'])) {
            $progressAttributes['source'] ??= UserDocumentProgress::SOURCE_ANALYTICS;
            $progressAttributes['action'] ??= UserDocumentProgress::ACTION_STATUS_CHANGE;
            $progressAttributes['created_by'] ??= $this->haveSystemUser()->employee_id;
            $progress = $this->haveRecord(UserDocumentProgresses::class, [
                ...$progressAttributes,
                'created_at' => $userKycAttributes['status_updated_at'],
                ...Arr::leaveOnlyKeys($userKycAttributes, ['site_id', 'user_id', 'kyc_status']),
            ]);
            self::assertInstanceOf(UserDocumentProgress::class, $progress);

            [$lastRequestId, $lastWaitId] = match ($progress->kyc_status) {
                UserKyc::KYC_REQUEST, UserKyc::KYC_REQUEST_REPEAT => [$progress->id, null],
                UserKyc::KYC_WAIT, UserKyc::KYC_WAIT_WITHOUT_REQUEST => [null, $progress->id],
                default => [null, null],
            };
            $userKycAttributes = array_merge($userKycAttributes, array_filter([
                'last_progress_id' => $progress->id,
                'last_request_progress_id' => $lastRequestId,
                'last_wait_progress_id' => $lastWaitId
            ]));
        }

        $userKyc = $this->haveRecord(UserKycs::class, $userKycAttributes);
        self::assertInstanceOf(UserKyc::class, $userKyc);
        return $userKyc;
    }

    protected static function uniqSiteId(array $platformSites = [Site::PLATFORM_SITES_SMEN, Site::PLATFORM_SITES_GI, Site::PLATFORM_SITES_YS]): int
    {
        static $lastSiteKey = 0;
        $sites = array_merge(...$platformSites);


        if ($lastSiteKey >= count($sites)) {
            $lastSiteKey %= count($sites);
        }

        $siteId = $sites[$lastSiteKey];
        $lastSiteKey++;

        return $siteId;
    }

    protected static function uniqRuntimeId(): int
    {
        static $lastUserId = 1;
        return $lastUserId++;
    }

    protected static function uniqRuntimeUuid(): string
    {
        return UuidHelper::v5(UuidHelper::nil(), self::uniqRuntimeId());
    }

    protected static function randomCurrency(): string
    {
        return self::randomItem([Rate::UAH, Rate::USD, Rate::EUR, Rate::RUB]);
    }

    protected static function randomMoney(int $min = 0, int $max = 1000): string
    {
        return number_format((float)(random_int($min, $max) . '.' . mt_rand()), 2, '.', '');
    }

    protected static function randomItem(array $items)
    {
        return $items[array_rand($items)];
    }
}
