<?php

declare(strict_types=1);

namespace app\tests\unit\repositories;

use app\back\repositories\Refcodes;
use app\back\repositories\Users;
use app\back\repositories\WpWebmasters;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Yiisoft\Db\Query\Query;

class RefcodesTest extends TestCase
{
    use DbTransactionalUnitTrait;

    private const string WP_AFF_OWNER_VP = 'V.P';

    #[DataProvider('positiveRefcodesAppIdDataProvider')]
    public function testPositiveRefcodeAppIdExpression(string $code, string $appId): void
    {
        $this->haveSystemUser();
        $this->haveRecords(Refcodes::class, [
            ['code' => $code]
        ]);

        $testData = (new Query($this->db()))
            ->select(['appId' => Refcodes::appIdExpression()])
            ->from(['r' => Refcodes::TABLE_NAME])
            ->where(['r.code' => $code])
            ->one();

        self::assertEquals($appId, $testData['appId']);
    }

    #[DataProvider('negativeRefcodesAppIdDataProvider')]
    public function testNegativeRefcodeAppIdExpression(string $code, string | null $appId): void
    {
        $this->haveSystemUser();
        $this->haveRecords(Refcodes::class, [
            ['code' => $code]
        ]);

        $testData = (new Query($this->db()))
            ->select(['appId' => Refcodes::appIdExpression()])
            ->from(['r' => Refcodes::TABLE_NAME])
            ->where(['r.code' => $code])
            ->one();

        self::assertEquals($appId, $testData['appId']);
    }

    #[DataProvider('webmastersDataProvider')]
    public function testWebmasterIdExtract(string $webmaster, string | null $id): void
    {
        preg_match('#' . Refcodes::webmasterIdExp() . '#', $webmaster, $matches);
        self::assertEquals($id, !empty($matches[1]) ? $matches[1] : ($matches[2] ?? null));
    }

    public function testAffOwnerWithYsWebmasterId(): void
    {
        $this->haveRates();

        $wpWebmasterId = 345645;
        $wpWebmasterId2 = 123456;
        $ysWebmasterId = '19c5ba';

        $codeWp = 'ca_w' . $wpWebmasterId . 'p1961_29BransonT244';
        $codeYs = 'aff_' . $ysWebmasterId . '_29_affid_subaffid_subid2_world30wide';
        $codeYs2 = 'aff_' . $wpWebmasterId2 . '_41_BBDPLfree_subaffid_subid2_worldwide'; //webmaster_id contains only numbers without letters

        $siteId = self::uniqSiteId();

        $this->haveWpWebmasterRecord(['id' => $wpWebmasterId, 'aff_owner' => self::WP_AFF_OWNER_VP]);
        $this->haveWpWebmasterRecord(['id' => $wpWebmasterId2, 'aff_owner' => self::WP_AFF_OWNER_VP]);

        $refcode1 = $this->haveRefcodeRecord(['code' => $codeWp, 'webmaster_id' => (string)$wpWebmasterId]);
        $refcodeYs = $this->haveRefcodeRecord(['code' => $codeYs, 'webmaster_id' => $ysWebmasterId]);
        $refcodeYs2 = $this->haveRefcodeRecord(['code' => $codeYs2, 'webmaster_id' => (string)$wpWebmasterId2]);

        $this->haveRecords(Users::class, [
            ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId(), 'refcode_id' => $refcode1->id],
            ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId(), 'refcode_id' =>  $refcodeYs->id],
            ['site_id' => $siteId, 'user_id' => self::uniqRuntimeId(), 'refcode_id' =>  $refcodeYs2->id],
        ]);

        $testData = (new Query($this->db()))
            ->select(['r.code', 'r.webmaster_id', 'wp_w.aff_owner'])
            ->from(['u' => Users::TABLE_NAME])
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id')
            ->leftJoin(['wp_w' => WpWebmasters::TABLE_NAME], Refcodes::wpJoinExpression('wp_w.id'))
            ->where(['u.site_id' => $siteId])
            ->groupBy(['r.code', 'r.webmaster_id', 'wp_w.aff_owner'])
            ->orderBy('r.code')
            ->all();

        self::assertSame([
            ['code' => $refcodeYs2->code, 'webmaster_id' =>  $refcodeYs2->webmaster_id, 'aff_owner' => null],
            ['code' => $refcodeYs->code, 'webmaster_id' => $refcodeYs->webmaster_id, 'aff_owner' => null],
            ['code' => $refcode1->code, 'webmaster_id' => $refcode1->webmaster_id, 'aff_owner' => self::WP_AFF_OWNER_VP],
        ], $testData);
    }

    public static function positiveRefcodesAppIdDataProvider(): array
    {
        return [
            ['code' => 'sp_BQAWDQAAHUMAACAcAAA.2019-09.06_2266-android-wagner_1_17431c42f86fa1f344c4e4a155b23264e', 'appId' => '2266'],
            ['code' => 'sp_BQAWDQAAHUMAACAcAAA.2020-06.19.4741-ios-blackbody_1_1_d3b554984e0b9370cace620a7ef762ab', 'appId' => '4741'],
            ['code' => 'sp_BQAWDQAAHUMAAFEcAAA.2020-06.17.787_ios-germann_1_1_2b8aff156c806b74ed12004f06449e76', 'appId' => '787'],
            ['code' => 'sp_BQAWDQAAHUMAACAcAAA.2020-06.10_4741-ios-blackbody_1_1_d3b554984e0b9370cace620a7ef762ab', 'appId' => '4741'],
            ['code' => 'sp_BQAWDQAAHUMAACAcAAA.2020-06.10.4213_android-antik_1_1_1227de378b3f634f4b28881be6d2f848', 'appId' => '4213'],
            ['code' => 'wp_w51306p138_10639-outsource-fbpwa', 'appId' => '10639'],
            ['code' => 'wp_w51306p138.10641-outsource-fbpwa', 'appId' => '10641'],
            ['code' => 'wp_w51306p138_10642_outsource-fbpwa', 'appId' => '10642'],
            ['code' => 'wp_w51306p43_FB_frin_7464_Mahova11_23845374536340542', 'appId' => '7464'],
            ['code' => 'wp_w51306p43_FB_Gefasst_10249_Alik11_KZ', 'appId' => '10249'],
        ];
    }

    public static function negativeRefcodesAppIdDataProvider(): array
    {
        return [
            ['code' => 'wp_w51306p138_10644-outsourcefbpwa', 'appId' => null],
            ['code' => 'wp_w51306grup43_4949-androidoutsource_reg', 'appId' => null],
            ['code' => 'wp_w51306grup43_7133-ioswagner_Reg', 'appId' => null],
            ['code' => 'wp_w51306gmdp43_3138-ios.spooler_Reg', 'appId' => null],
            ['code' => 'wp_w51306gmdp43_4783-ios_blackbody_Reg', 'appId' => null],
            ['code' => 'wp_w51306p43-FB-frin-7464-Mahova12_23845374536340542', 'appId' => null],
            ['code' => 'wp_w51306p43.FB.frin.7464_Mahova13_23845374536340542', 'appId' => null],
            ['code' => 'wp_w51306p43-FB-Gefasst-10249_Alik15_KZ', 'appId' => null],
        ];
    }

    public static function webmastersDataProvider(): array
    {
        return [
            ['wp_w51306p138_10644-outsourcefbpwa', '51306'],
            ['netref_655244', '655244'],
            ['wp_w51306gczp624_20300-android-outsource', '51306'],
            ['va_w106216c91940l9779guzp667_bb7yr309', '106216'],
            ['va_w138695c103497l7177guap479_372aef2e-5bc0-4c6f-b115-b052d6bcca3e', '138695'],
            ['wp_w00001grup43_tracker3', '00001'],
            ['wp_w1p1', '1'],
            ['vip911897_autotest', '911897'],
            ['vip104562_cstr-43007-48dac406-779439', '104562'],
            ['vip100007', '100007'],
            ['vip16_cryptcoincasbuy', '16'],

            ['sp_BQAWDQAAHUMAACAcAAA.2020-06.10.4213_android-antik_1_1_1227de378b3f634f4b28881be6d2f848', null],

            ['aff_023fd5_34_GooglePlay', '023fd5'],
            ['aff_016f7b_SCnet', '016f7b'],
            ['aff_0599bc_7_Adbet_MRB_BR_mob_300x300_bet_gaveanews.com_1985836698_50betnoriskmb', '0599bc'],

            ['aff_qwerty', null],
            ['aff_094d3_345_22704', null],
            ['aff_f6test_1_never_DAY', null],
            ['rm_vp_to_mbs_banner_ysvprmfixed366_source_aff_57fdb6_34_GooglePlay_178', null],
        ];
    }
}
