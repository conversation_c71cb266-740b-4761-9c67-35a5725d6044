<?php

declare(strict_types=1);

namespace app\tests\unit\repositories;

use app\back\components\DateTimeImmutableWithMicroseconds;
use app\back\components\helpers\Db;
use app\back\components\PgArray;
use app\back\components\PgVector;
use app\back\entities\BaseEntity;
use app\back\repositories\BaseRepository;
use app\back\repositories\DbModifyHelper;
use app\back\repositories\UserTransactions;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\TestCase;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class DbModifyHelperTest extends TestCase
{
    use DbTransactionalUnitTrait;

    public function testEmptyTypecasts(): void
    {
        $this->haveRates();
        $siteId = self::uniqSiteId();

        $stat1 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' =>  self::uniqRuntimeId(),
            'ip' => '127.0.0.1',
            'country' => 'UA',
        ]);

        $stat2 = $this->haveUserTransactionRecord([
            'site_id' => $siteId,
            'user_id' =>  self::uniqRuntimeId(),
            'country' => '',
        ]);

        $this->container()->get(UserTransactions::class)->batchUpdateDistinct([
            [
                'site_id' => $stat1->site_id,
                'transaction_id' => $stat1->transaction_id,
                'ip' => '*********',
                'country' => '',
            ],
            [
                'site_id' => $stat2->site_id,
                'transaction_id' => $stat2->transaction_id,
                'ip' => '*********',
                'country' => null,
            ]
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $stat1->site_id,
            'transaction_id' => $stat1->transaction_id,
        ], [
            'ip' => '*********',
            'country' => '',
        ]);

        $this->seeRecordWithFields(UserTransactions::class, [
            'site_id' => $stat2->site_id,
            'transaction_id' => $stat2->transaction_id,
        ], [
            'ip' => '*********',
            'country' => null,
        ]);
    }

    public function testPrepareProp(): void
    {
        $entity = new class extends BaseEntity {
            public int $id;
            public bool $prop_as_bool;
            public int $prop_as_int;
            public string $prop_as_text;
            public array $prop_as_jsonb;
            public \DateTimeImmutable $prop_as_datetime;
            public DateTimeImmutableWithMicroseconds $prop_as_datetime_micro;
            public \DateInterval $prop_as_interval;
            public PgVector $prop_as_pg_vector;
            public PgArray $prop_as_pg_array;
        };

        define('TMP_TEST_PREPARE_PROP_ENTITY_CLASS', $entity::class);
        $repo = new class ($this->db()) extends BaseRepository {
            public const string ENTITY_CLASS = TMP_TEST_PREPARE_PROP_ENTITY_CLASS;
            public const string TABLE_NAME = 'tmp_test_prepare_prop_table';
            public const array PRIMARY_KEY = ['id'];
        };

        Db::createTempTable($this->db(), $repo::TABLE_NAME, (new Query($this->db()))->select([
            'id' => '(2)',
            'prop_as_bool' => "(null::bool)",
            'prop_as_int' => "(null::int)",
            'prop_as_text' => "(null::text)",
            'prop_as_jsonb' => "(null::jsonb)",
            'prop_as_datetime' => "(null::timestamp(0))",
            'prop_as_datetime_micro' => "(null::timestamp)",
            'prop_as_interval' => "(null::interval)",
            'prop_as_pg_vector' => "(null::vector)",
            'prop_as_pg_array' => "(null::text[])",
        ]), ['id']);

        $entity->id = 1;
        $entity->prop_as_bool = true;
        $entity->prop_as_int = 100;
        $entity->prop_as_text = 'xyz';
        $entity->prop_as_jsonb = ['x' => [true, false, 0, '1', null, ['y' => []]]];
        $entity->prop_as_datetime = new \DateTimeImmutable();
        $entity->prop_as_datetime_micro = new DateTimeImmutableWithMicroseconds();
        $entity->prop_as_interval = new \DateInterval('PT1S');
        $entity->prop_as_pg_vector = new PgVector([1, 2, 3]);
        $entity->prop_as_pg_array = new PgArray(['a', 'b', 'c']);
        $props = get_object_vars($entity);
        $repo->insert($entity);

        $this->seeRecordWithFields($repo::class, ['id' => 1], $props);
    }

    public function testPgArrayPrepareProp(): void
    {
        $repo = new class ($this->db()) {
            use DbModifyHelper;
            public function __construct(private readonly ConnectionInterface $db)
            {
            }
        };

        $actualSql = (new Query($this->db()))
            ->select([
                'prop_as_pg_array_default' => $repo->prepareProp(new PgArray([1, 2, 3])),
                'prop_as_pg_array_int' => $repo->prepareProp(new PgArray([4, 5, 6], PgArray::TYPE_INT)),
                'prop_as_pg_array_varchar' => $repo->prepareProp(new PgArray([7, 8, 9], PgArray::TYPE_VARCHAR)),
                'prop_as_pg_array_jsonb' => $repo->prepareProp(new PgArray([[7], [8], [9, 0]], PgArray::TYPE_JSONB)),
            ])
            ->createCommand()
            ->getRawSql();

        self::assertSame('SELECT ' .
            'ARRAY[1, 2, 3] AS "prop_as_pg_array_default", ' .
            'ARRAY[4, 5, 6]::int[] AS "prop_as_pg_array_int", ' .
            'ARRAY[7, 8, 9]::varchar[] AS "prop_as_pg_array_varchar", ' .
            "ARRAY['[7]', '[8]', '[9,0]']::jsonb[] AS \"prop_as_pg_array_jsonb\"", $actualSql);
    }
}
