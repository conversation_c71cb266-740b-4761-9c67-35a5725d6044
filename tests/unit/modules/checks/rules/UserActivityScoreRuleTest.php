<?php

declare(strict_types=1);

namespace app\tests\unit\modules\checks\rules;

use app\back\components\helpers\Arr;
use app\back\config\tasks\Res;
use app\back\entities\UserTransaction;
use app\back\modules\checks\checks\rules\BaseRule;
use app\back\modules\checks\checks\rules\UserActivityScoreRule;
use app\back\repositories\UserGamePaymentActivities;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerUnitTrait;
use Yiisoft\Db\Query\Query;

class UserActivityScoreRuleTest extends BaseActionTestCase
{
    use CheckRulesTrait;
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;

    private string $pk;

    public function setUp(): void
    {
        parent::setUp();

        $siteId = self::uniqSiteId();
        $userId = self::uniqRuntimeId();
        $this->pk = $siteId . '-' . $userId;
        $this->haveRates();

        $date = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('- 12 day')));
        $this->haveUserRecord(['site_id' => $siteId, 'user_id' => $userId, 'date' => $date]);
        $this->haveUserTransactionRecord(['created_at' => $date, 'site_id' => $siteId, 'user_id' => $userId, 'op_id' => UserTransaction::OP_IN, 'status' => UserTransaction::STATUS_SUCCESS]);
        $this->haveUserGameTokenRecord(['created_at' => $date, 'site_id' => $siteId, 'user_id' => $userId]);

        $date2 = new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime('- 11 day')));
        $this->haveUserTransactionRecord(['created_at' => $date2, 'site_id' => $siteId, 'user_id' => $userId, 'op_id' => UserTransaction::OP_IN, 'status' => UserTransaction::STATUS_SUCCESS]);
        $this->haveUserGameTokenRecord(['created_at' => $date2, 'site_id' => $siteId, 'user_id' => $userId]);

        $this->runTask('update-users-games-payments-activity', Res::DEFAULT, self::LAST_MINUTE_PERIOD);
        $actual = (new Query($this->db()))
            ->select(['score'])
            ->from(UserGamePaymentActivities::TABLE_NAME)
            ->where(['site_id' => $siteId, 'user_id' => $userId])
            ->scalar();
        $this->assertSame(4, $actual);
    }

    public function testBasic(): void
    {
        $rule = $this->container()->get(UserActivityScoreRule::class);

        Arr::configure($rule, ['score' => 4, 'scoreOperator' => BaseRule::OPERATOR_LOWER]);
        $this->assertCheckResult($rule, $this->pk, false, "the activity score is 4");

        Arr::configure($rule, ['score' => 4, 'scoreOperator' => BaseRule::OPERATOR_GREATER]);
        $this->assertCheckResult($rule, $this->pk, false, "the activity score is 4");

        Arr::configure($rule, ['score' => 4, 'scoreOperator' => BaseRule::OPERATOR_EQ]);
        $this->assertCheckResult($rule, $this->pk, true, "the activity score is 4");

        Arr::configure($rule, ['score' => 4, 'scoreOperator' => BaseRule::OPERATOR_GREATER_OR_EQ]);
        $this->assertCheckResult($rule, $this->pk, true, "the activity score is 4");

        Arr::configure($rule, ['score' => 4, 'scoreOperator' => BaseRule::OPERATOR_LOWER_OR_EQ]);
        $this->assertCheckResult($rule, $this->pk, true, "the activity score is 4");
    }
}
