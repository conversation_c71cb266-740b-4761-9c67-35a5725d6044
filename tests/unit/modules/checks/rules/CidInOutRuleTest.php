<?php

declare(strict_types=1);

namespace app\tests\unit\modules\checks\rules;

use app\back\components\helpers\Arr;
use app\back\entities\S2pOrder;
use app\back\modules\checks\checks\rules\BaseRule;
use app\back\modules\checks\checks\rules\CidInOutRule;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\FakerCheckUnitTrait;
use app\tests\libs\FakerUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\TestCase;

#[CoversClass(CidInOutRule::class)]
class CidInOutRuleTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;
    use FakerCheckUnitTrait;
    use CheckRulesTrait;

    public function testCidOut(): void
    {
        $this->haveRates();
        $cid = self::uniqRuntimeId();

        $siteUserId1 = ['site_id' => self::uniqSiteId(), 'user_id' => self::uniqRuntimeId()];
        $siteUserId2 = ['site_id' => self::uniqSiteId(), 'user_id' => self::uniqRuntimeId()];


        $rule = $this->container()->get(CidInOutRule::class);

        $this->haveUserRecord([...$siteUserId1, 'cid' => $cid]);
        $this->haveUserRecord([...$siteUserId2, 'cid' => $cid]);

        Arr::configure($rule, ['value' => 10, 'valueOperator' => BaseRule::OPERATOR_GREATER]);
        $this->haveS2pOrderRecord([ ...$siteUserId1, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'summ' => '5',
        ]);
        $this->haveS2pOrderRecord([ ...$siteUserId2, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'summ' => '5',
        ]);
        $this->assertCheckResult($rule, $cid, false);

        $this->haveS2pOrderRecord([ ...$siteUserId1, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_OUT,
            'summ' => '5',
        ]);
        $this->haveS2pOrderRecord([ ...$siteUserId2, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'summ' => '5',
        ]);
        $this->assertCheckResult($rule, $cid, false);

        $this->haveS2pOrderRecord([ ...$siteUserId1, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_IN,
            'summ' => '10',
        ]);
        $this->haveS2pOrderRecord([ ...$siteUserId2, 'status' => S2pOrder::STATUS_SUCCESS,
            'type' => S2pOrder::TYPE_OUT,
            'summ' => '5',
        ]);
        $this->assertCheckResult($rule, $cid, true);
    }
}
